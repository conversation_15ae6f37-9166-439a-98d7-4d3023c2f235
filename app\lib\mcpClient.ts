import { Tool, MCPServerConfig } from '@/types';

// Simplified MCP client for demo purposes
// In production, this would use the actual MCP SDK

export interface MCPClientManager {
  servers: Map<string, MCPServerInstance>;
  getAvailableTools(): Promise<Tool[]>;
  executeToolCall(serverName: string, toolName: string, args: any): Promise<any>;
  startServer(serverName: string, config: MCPServerConfig): Promise<void>;
  stopServer(serverName: string): Promise<void>;
  stopAllServers(): Promise<void>;
}

interface MCPServerInstance {
  name: string;
  config: MCPServerConfig;
  tools: Tool[];
  isConnected: boolean;
}

export class MCPClient implements MCPClientManager {
  public servers: Map<string, MCPServerInstance> = new Map();

  async getAvailableTools(): Promise<Tool[]> {
    const allTools: Tool[] = [];

    // Return demo tools for now
    for (const [serverName, server] of this.servers) {
      if (server.isConnected) {
        allTools.push(...server.tools);
      }
    }

    return allTools;
  }

  async executeToolCall(serverName: string, toolName: string, args: any): Promise<any> {
    const server = this.servers.get(serverName);
    if (!server || !server.isConnected) {
      throw new Error(`Server ${serverName} is not connected`);
    }

    // Simulate tool execution for demo
    console.log(`Executing ${toolName} on ${serverName} with args:`, args);

    // Return demo response based on tool
    if (toolName === 'search_web') {
      return {
        content: [{
          type: 'text',
          text: `Search results for "${args.query}": This is a demo response from the ${serverName} server.`
        }]
      };
    }

    return {
      content: [{
        type: 'text',
        text: `Tool ${toolName} executed successfully on ${serverName} server.`
      }]
    };
  }

  async startServer(serverName: string, config: MCPServerConfig): Promise<void> {
    if (this.servers.has(serverName)) {
      await this.stopServer(serverName);
    }

    try {
      // Create demo tools based on server type
      let demoTools: Tool[] = [];

      if (serverName === 'context7') {
        demoTools = [
          {
            type: 'function',
            function: {
              name: 'search_docs',
              description: 'Search through documentation and code context',
              parameters: {
                type: 'object',
                properties: {
                  query: { type: 'string', description: 'Search query' },
                  context: { type: 'string', description: 'Context type (docs, code, etc.)' }
                },
                required: ['query']
              }
            },
            serverName
          }
        ];
      } else if (serverName === 'exa') {
        demoTools = [
          {
            type: 'function',
            function: {
              name: 'search_web',
              description: 'Search the web using Exa API',
              parameters: {
                type: 'object',
                properties: {
                  query: { type: 'string', description: 'Search query' },
                  num_results: { type: 'number', description: 'Number of results', default: 10 }
                },
                required: ['query']
              }
            },
            serverName
          }
        ];
      }

      const serverInstance: MCPServerInstance = {
        name: serverName,
        config,
        tools: demoTools,
        isConnected: true
      };

      this.servers.set(serverName, serverInstance);
      console.log(`MCP server ${serverName} started successfully (demo mode)`);
    } catch (error) {
      console.error(`Failed to start MCP server ${serverName}:`, error);
      throw error;
    }
  }

  async stopServer(serverName: string): Promise<void> {
    const server = this.servers.get(serverName);
    if (!server) {
      return;
    }

    try {
      this.servers.delete(serverName);
      console.log(`MCP server ${serverName} stopped`);
    } catch (error) {
      console.error(`Error stopping MCP server ${serverName}:`, error);
      this.servers.delete(serverName);
    }
  }

  async stopAllServers(): Promise<void> {
    const stopPromises = Array.from(this.servers.keys()).map(serverName =>
      this.stopServer(serverName)
    );

    await Promise.all(stopPromises);
  }
}

// Singleton instance
export const mcpClient = new MCPClient();
