import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { spawn, ChildProcess } from 'child_process';
import { Tool, MCPServerConfig } from '@/types';

export interface MCPClientManager {
  servers: Map<string, MCPServerInstance>;
  getAvailableTools(): Promise<Tool[]>;
  executeToolCall(serverName: string, toolName: string, args: any): Promise<any>;
  startServer(serverName: string, config: MCPServerConfig): Promise<void>;
  stopServer(serverName: string): Promise<void>;
  stopAllServers(): Promise<void>;
}

interface MCPServerInstance {
  name: string;
  config: MCPServerConfig;
  client: Client;
  transport: StdioClientTransport;
  process: ChildProcess;
  tools: Tool[];
  isConnected: boolean;
}

export class MC<PERSON>lient implements MCPClientManager {
  public servers: Map<string, MCPServerInstance> = new Map();

  async getAvailableTools(): Promise<Tool[]> {
    const allTools: Tool[] = [];
    
    for (const [serverName, server] of this.servers) {
      if (server.isConnected) {
        try {
          // Refresh tools from server
          const response = await server.client.listTools();
          
          // Convert MCP tools to our Tool format
          const serverTools: Tool[] = response.tools.map(tool => ({
            type: 'function' as const,
            function: {
              name: tool.name,
              description: tool.description || '',
              parameters: tool.inputSchema || {
                type: 'object',
                properties: {},
                required: []
              }
            },
            serverName: serverName // Add server name for routing
          }));
          
          server.tools = serverTools;
          allTools.push(...serverTools);
        } catch (error) {
          console.error(`Error fetching tools from server ${serverName}:`, error);
        }
      }
    }
    
    return allTools;
  }

  async executeToolCall(serverName: string, toolName: string, args: any): Promise<any> {
    const server = this.servers.get(serverName);
    if (!server || !server.isConnected) {
      throw new Error(`Server ${serverName} is not connected`);
    }

    try {
      const response = await server.client.callTool({
        name: toolName,
        arguments: args
      });

      return response;
    } catch (error) {
      console.error(`Error executing tool ${toolName} on server ${serverName}:`, error);
      throw error;
    }
  }

  async startServer(serverName: string, config: MCPServerConfig): Promise<void> {
    if (this.servers.has(serverName)) {
      await this.stopServer(serverName);
    }

    try {
      // Spawn the MCP server process
      const childProcess = spawn(config.command, config.args, {
        stdio: ['pipe', 'pipe', 'pipe'],
        env: { ...process.env, ...config.env }
      });

      // Create transport and client
      const transport = new StdioClientTransport({
        reader: childProcess.stdout,
        writer: childProcess.stdin
      });

      const client = new Client({
        name: 'ollama-mcp-chat',
        version: '1.0.0'
      }, {
        capabilities: {
          tools: {}
        }
      });

      // Connect to the server
      await client.connect(transport);

      // Initialize the connection
      await client.initialize();

      const serverInstance: MCPServerInstance = {
        name: serverName,
        config,
        client,
        transport,
        process: childProcess,
        tools: [],
        isConnected: true
      };

      this.servers.set(serverName, serverInstance);

      // Handle process events
      childProcess.on('error', (error) => {
        console.error(`MCP server ${serverName} process error:`, error);
        serverInstance.isConnected = false;
      });

      childProcess.on('exit', (code) => {
        console.log(`MCP server ${serverName} exited with code ${code}`);
        serverInstance.isConnected = false;
        this.servers.delete(serverName);
      });

      // Fetch initial tools
      await this.getAvailableTools();

      console.log(`MCP server ${serverName} started successfully`);
    } catch (error) {
      console.error(`Failed to start MCP server ${serverName}:`, error);
      throw error;
    }
  }

  async stopServer(serverName: string): Promise<void> {
    const server = this.servers.get(serverName);
    if (!server) {
      return;
    }

    try {
      if (server.isConnected) {
        await server.client.close();
      }
      
      if (server.process && !server.process.killed) {
        server.process.kill('SIGTERM');
        
        // Wait for graceful shutdown, then force kill if needed
        setTimeout(() => {
          if (!server.process.killed) {
            server.process.kill('SIGKILL');
          }
        }, 5000);
      }

      this.servers.delete(serverName);
      console.log(`MCP server ${serverName} stopped`);
    } catch (error) {
      console.error(`Error stopping MCP server ${serverName}:`, error);
      this.servers.delete(serverName);
    }
  }

  async stopAllServers(): Promise<void> {
    const stopPromises = Array.from(this.servers.keys()).map(serverName => 
      this.stopServer(serverName)
    );
    
    await Promise.all(stopPromises);
  }
}

// Singleton instance
export const mcpClient = new MCPClient();

// Cleanup on process exit
process.on('exit', () => {
  mcpClient.stopAllServers();
});

process.on('SIGINT', () => {
  mcpClient.stopAllServers();
  process.exit(0);
});

process.on('SIGTERM', () => {
  mcpClient.stopAllServers();
  process.exit(0);
});
