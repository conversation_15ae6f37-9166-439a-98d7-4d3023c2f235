"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/components/ChatWindow/ChatWindow.tsx":
/*!**************************************************!*\
  !*** ./app/components/ChatWindow/ChatWindow.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatWindow)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Typography,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Typography,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Typography,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Typography,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _MessageBubble__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MessageBubble */ \"(app-pages-browser)/./app/components/ChatWindow/MessageBubble.tsx\");\n/* harmony import */ var _InputBar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./InputBar */ \"(app-pages-browser)/./app/components/ChatWindow/InputBar.tsx\");\n/* harmony import */ var _TypingIndicator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../TypingIndicator */ \"(app-pages-browser)/./app/components/TypingIndicator.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ChatWindow(param) {\n    let { selectedModel, availableTools, conversation, onMessageSent, modelCapabilities } = param;\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const theme = (0,_barrel_optimize_names_Alert_Box_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    const messages = (conversation === null || conversation === void 0 ? void 0 : conversation.messages) || [];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatWindow.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ChatWindow.useEffect\"], [\n        messages\n    ]);\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: 'smooth'\n        });\n    };\n    const handleSendMessage = async (content, attachments)=>{\n        if (!selectedModel) {\n            setError('Please select a model first');\n            return;\n        }\n        // Create user message\n        const userMessage = {\n            id: (0,uuid__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(),\n            role: 'user',\n            content,\n            timestamp: new Date(),\n            attachments\n        };\n        onMessageSent(userMessage);\n        setLoading(true);\n        setError(null);\n        try {\n            // Check if message has vision content\n            const hasVisionContent = attachments === null || attachments === void 0 ? void 0 : attachments.some((att)=>att.type === 'image');\n            // Validate vision capability\n            if (hasVisionContent && !(modelCapabilities === null || modelCapabilities === void 0 ? void 0 : modelCapabilities.supportsVision)) {\n                setError(\"Model \".concat(selectedModel, \" does not support vision/image processing. Please select a vision-capable model like llava or qwen2-vl.\"));\n                setLoading(false);\n                return;\n            }\n            // Prepare messages for API\n            const conversationMessages = [\n                ...messages,\n                userMessage\n            ];\n            // Only include tools if model supports them\n            const toolsToSend = (modelCapabilities === null || modelCapabilities === void 0 ? void 0 : modelCapabilities.supportsTools) ? availableTools : [];\n            console.log('ChatWindow - Available tools:', availableTools);\n            console.log('ChatWindow - Model capabilities:', modelCapabilities);\n            console.log('ChatWindow - Tools to send:', toolsToSend);\n            // Send to Ollama API\n            const response = await fetch('/api/ollama', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    model: selectedModel,\n                    messages: conversationMessages.map((msg)=>({\n                            role: msg.role,\n                            content: msg.content,\n                            ...msg.toolCall && {\n                                tool_calls: [\n                                    msg.toolCall\n                                ]\n                            }\n                        })),\n                    tools: toolsToSend,\n                    hasVisionContent\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to get response from Ollama');\n            }\n            const data = await response.json();\n            const assistantMessage = data.message;\n            console.log('Received assistant message:', assistantMessage);\n            console.log('Model capabilities:', modelCapabilities);\n            // Create assistant message\n            const newAssistantMessage = {\n                id: (0,uuid__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(),\n                role: 'assistant',\n                content: assistantMessage.content,\n                timestamp: new Date()\n            };\n            // Check if there are tool calls (only if model supports tools)\n            if ((modelCapabilities === null || modelCapabilities === void 0 ? void 0 : modelCapabilities.supportsTools) && assistantMessage.tool_calls && assistantMessage.tool_calls.length > 0) {\n                console.log('Tool calls detected:', assistantMessage.tool_calls);\n                const toolCall = assistantMessage.tool_calls[0];\n                // Add an ID to the tool call if it doesn't have one\n                if (!toolCall.id) {\n                    toolCall.id = (0,uuid__WEBPACK_IMPORTED_MODULE_6__[\"default\"])();\n                }\n                newAssistantMessage.toolCall = toolCall;\n                // Add the assistant message with tool call\n                onMessageSent(newAssistantMessage);\n                // Execute the tool call\n                await executeToolCall(toolCall, [\n                    ...messages,\n                    userMessage,\n                    newAssistantMessage\n                ]);\n            } else {\n                // Add the assistant message\n                onMessageSent(newAssistantMessage);\n            }\n        } catch (err) {\n            console.error('Error sending message:', err);\n            setError('Failed to send message. Please try again.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const executeToolCall = async (toolCall, currentMessages)=>{\n        try {\n            console.log('Executing tool call:', toolCall);\n            // Parse tool arguments\n            let toolArgs;\n            try {\n                toolArgs = typeof toolCall.function.arguments === 'string' ? JSON.parse(toolCall.function.arguments) : toolCall.function.arguments;\n            } catch (parseError) {\n                console.error('Failed to parse tool arguments:', parseError);\n                toolArgs = toolCall.function.arguments;\n            }\n            console.log('Tool arguments:', toolArgs);\n            // Determine server name based on tool name\n            let serverName = 'exa'; // default\n            if (toolCall.function.name === 'get_weather') {\n                serverName = 'weather';\n            } else if (toolCall.function.name === 'search_web') {\n                serverName = 'exa';\n            }\n            console.log(\"Using server: \".concat(serverName, \" for tool: \").concat(toolCall.function.name));\n            // Execute tool via MCP API\n            const response = await fetch('/api/mcp', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'execute_tool',\n                    serverName: serverName,\n                    toolName: toolCall.function.name,\n                    arguments: toolArgs\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to execute tool');\n            }\n            const toolResult = await response.json();\n            // Create tool result message\n            const toolResultMessage = {\n                id: (0,uuid__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(),\n                role: 'tool',\n                content: JSON.stringify(toolResult.result, null, 2),\n                timestamp: new Date(),\n                toolResult: {\n                    toolCallId: toolCall.id,\n                    result: toolResult.result\n                }\n            };\n            onMessageSent(toolResultMessage);\n            // Send the tool result back to Ollama for final response\n            const finalResponse = await fetch('/api/ollama', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    model: selectedModel,\n                    messages: [\n                        ...currentMessages,\n                        toolResultMessage\n                    ].map((msg)=>({\n                            role: msg.role,\n                            content: msg.content,\n                            ...msg.toolCall && {\n                                tool_calls: [\n                                    msg.toolCall\n                                ]\n                            }\n                        })),\n                    tools: availableTools\n                })\n            });\n            if (finalResponse.ok) {\n                const finalData = await finalResponse.json();\n                const finalMessage = {\n                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(),\n                    role: 'assistant',\n                    content: finalData.message.content,\n                    timestamp: new Date()\n                };\n                onMessageSent(finalMessage);\n            }\n        } catch (err) {\n            console.error('Error executing tool call:', err);\n            // Add error message\n            const errorMessage = {\n                id: (0,uuid__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(),\n                role: 'assistant',\n                content: 'Sorry, I encountered an error while using the tool. Please try again.',\n                timestamp: new Date()\n            };\n            onMessageSent(errorMessage);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n        display: \"flex\",\n        flexDirection: \"column\",\n        height: \"100%\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                children: error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: -20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        y: -20\n                    },\n                    transition: {\n                        duration: 0.3\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        sx: {\n                            p: 2\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            severity: \"error\",\n                            onClose: ()=>setError(null),\n                            sx: {\n                                borderRadius: 2,\n                                backdropFilter: 'blur(20px)',\n                                background: 'rgba(255, 68, 68, 0.1)',\n                                border: '1px solid rgba(255, 68, 68, 0.3)',\n                                color: '#ff4444',\n                                '& .MuiAlert-icon': {\n                                    color: '#ff4444'\n                                }\n                            },\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                    lineNumber: 288,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                lineNumber: 286,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                flex: 1,\n                overflow: \"auto\",\n                sx: {\n                    px: 3,\n                    py: 2\n                },\n                children: [\n                    availableTools.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToolStatusDisplay, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 39\n                    }, this),\n                    messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.5\n                        },\n                        style: {\n                            height: '100%'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            display: \"flex\",\n                            flexDirection: \"column\",\n                            alignItems: \"center\",\n                            justifyContent: \"center\",\n                            height: \"100%\",\n                            textAlign: \"center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    variant: \"h6\",\n                                    sx: {\n                                        color: 'rgba(255, 255, 255, 0.8)',\n                                        mb: 1,\n                                        fontWeight: 500\n                                    },\n                                    children: conversation ? 'Start chatting' : 'Select a conversation'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    variant: \"body2\",\n                                    sx: {\n                                        color: 'rgba(255, 255, 255, 0.6)',\n                                        maxWidth: 400\n                                    },\n                                    children: conversation ? 'Type a message to begin the conversation with your AI assistant' : 'Choose a conversation from the sidebar or create a new one to get started'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                            lineNumber: 328,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                        children: messages.map((message, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: index * 0.1,\n                                    duration: 0.3\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MessageBubble__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    message: message,\n                                    isUser: message.role === 'user'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 17\n                                }, this)\n                            }, message.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                        lineNumber: 361,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                        children: loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            exit: {\n                                opacity: 0,\n                                y: -20\n                            },\n                            transition: {\n                                duration: 0.3\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TypingIndicator__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                            lineNumber: 381,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                        lineNumber: 379,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                        lineNumber: 392,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                lineNumber: 317,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                sx: {\n                    p: 3,\n                    borderTop: '1px solid rgba(255, 255, 255, 0.1)'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InputBar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    onSendMessage: handleSendMessage,\n                    disabled: !selectedModel || !conversation,\n                    loading: loading,\n                    modelCapabilities: modelCapabilities\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                    lineNumber: 397,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                lineNumber: 396,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n        lineNumber: 284,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatWindow, \"yBUK5kIkV3sR5SA/URI0pxGhTuo=\", false, function() {\n    return [\n        _barrel_optimize_names_Alert_Box_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n});\n_c = ChatWindow;\nvar _c;\n$RefreshReg$(_c, \"ChatWindow\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/ChatWindow/ChatWindow.tsx\n"));

/***/ })

});