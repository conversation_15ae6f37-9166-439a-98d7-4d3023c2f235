"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/components/ChatWindow/ChatWindow.tsx":
/*!**************************************************!*\
  !*** ./app/components/ChatWindow/ChatWindow.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatWindow)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Typography,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Typography,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Typography,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Typography,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _MessageBubble__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MessageBubble */ \"(app-pages-browser)/./app/components/ChatWindow/MessageBubble.tsx\");\n/* harmony import */ var _InputBar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./InputBar */ \"(app-pages-browser)/./app/components/ChatWindow/InputBar.tsx\");\n/* harmony import */ var _TypingIndicator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../TypingIndicator */ \"(app-pages-browser)/./app/components/TypingIndicator.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ChatWindow(param) {\n    let { selectedModel, availableTools, conversation, onMessageSent, modelCapabilities } = param;\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const theme = (0,_barrel_optimize_names_Alert_Box_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    const messages = (conversation === null || conversation === void 0 ? void 0 : conversation.messages) || [];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatWindow.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ChatWindow.useEffect\"], [\n        messages\n    ]);\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: 'smooth'\n        });\n    };\n    const handleSendMessage = async (content, attachments)=>{\n        if (!selectedModel) {\n            setError('Please select a model first');\n            return;\n        }\n        // Create user message\n        const userMessage = {\n            id: (0,uuid__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(),\n            role: 'user',\n            content,\n            timestamp: new Date(),\n            attachments\n        };\n        onMessageSent(userMessage);\n        setLoading(true);\n        setError(null);\n        try {\n            // Check if message has vision content\n            const hasVisionContent = attachments === null || attachments === void 0 ? void 0 : attachments.some((att)=>att.type === 'image');\n            // Validate vision capability\n            if (hasVisionContent && !(modelCapabilities === null || modelCapabilities === void 0 ? void 0 : modelCapabilities.supportsVision)) {\n                setError(\"Model \".concat(selectedModel, \" does not support vision/image processing. Please select a vision-capable model like llava or qwen2-vl.\"));\n                setLoading(false);\n                return;\n            }\n            // Prepare messages for API\n            const conversationMessages = [\n                ...messages,\n                userMessage\n            ];\n            // Only include tools if model supports them\n            const toolsToSend = (modelCapabilities === null || modelCapabilities === void 0 ? void 0 : modelCapabilities.supportsTools) ? availableTools : [];\n            console.log('ChatWindow - Available tools:', availableTools);\n            console.log('ChatWindow - Model capabilities:', modelCapabilities);\n            console.log('ChatWindow - Tools to send:', toolsToSend);\n            // Send to Ollama API\n            const response = await fetch('/api/ollama', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    model: selectedModel,\n                    messages: conversationMessages.map((msg)=>({\n                            role: msg.role,\n                            content: msg.content,\n                            ...msg.toolCall && {\n                                tool_calls: [\n                                    msg.toolCall\n                                ]\n                            }\n                        })),\n                    tools: toolsToSend,\n                    hasVisionContent\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to get response from Ollama');\n            }\n            const data = await response.json();\n            const assistantMessage = data.message;\n            console.log('Received assistant message:', assistantMessage);\n            console.log('Model capabilities:', modelCapabilities);\n            // Create assistant message\n            const newAssistantMessage = {\n                id: (0,uuid__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(),\n                role: 'assistant',\n                content: assistantMessage.content,\n                timestamp: new Date()\n            };\n            // Check if there are tool calls (only if model supports tools)\n            if ((modelCapabilities === null || modelCapabilities === void 0 ? void 0 : modelCapabilities.supportsTools) && assistantMessage.tool_calls && assistantMessage.tool_calls.length > 0) {\n                console.log('Tool calls detected:', assistantMessage.tool_calls);\n                const toolCall = assistantMessage.tool_calls[0];\n                // Add an ID to the tool call if it doesn't have one\n                if (!toolCall.id) {\n                    toolCall.id = (0,uuid__WEBPACK_IMPORTED_MODULE_6__[\"default\"])();\n                }\n                newAssistantMessage.toolCall = toolCall;\n                // Add the assistant message with tool call\n                onMessageSent(newAssistantMessage);\n                // Execute the tool call\n                await executeToolCall(toolCall, [\n                    ...messages,\n                    userMessage,\n                    newAssistantMessage\n                ]);\n            } else {\n                // Add the assistant message\n                onMessageSent(newAssistantMessage);\n            }\n        } catch (err) {\n            console.error('Error sending message:', err);\n            setError('Failed to send message. Please try again.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const executeToolCall = async (toolCall, currentMessages)=>{\n        try {\n            console.log('Executing tool call:', toolCall);\n            // Parse tool arguments\n            let toolArgs;\n            try {\n                toolArgs = typeof toolCall.function.arguments === 'string' ? JSON.parse(toolCall.function.arguments) : toolCall.function.arguments;\n            } catch (parseError) {\n                console.error('Failed to parse tool arguments:', parseError);\n                toolArgs = toolCall.function.arguments;\n            }\n            console.log('Tool arguments:', toolArgs);\n            // Determine server name based on tool name\n            let serverName = 'exa'; // default\n            if (toolCall.function.name === 'get_weather') {\n                serverName = 'weather';\n            } else if (toolCall.function.name === 'search_web') {\n                serverName = 'exa';\n            }\n            console.log(\"Using server: \".concat(serverName, \" for tool: \").concat(toolCall.function.name));\n            // Execute tool via MCP API\n            const response = await fetch('/api/mcp', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'execute_tool',\n                    serverName: serverName,\n                    toolName: toolCall.function.name,\n                    arguments: toolArgs\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to execute tool');\n            }\n            const toolResult = await response.json();\n            // Create tool result message\n            const toolResultMessage = {\n                id: (0,uuid__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(),\n                role: 'tool',\n                content: JSON.stringify(toolResult.result, null, 2),\n                timestamp: new Date(),\n                toolResult: {\n                    toolCallId: toolCall.id,\n                    result: toolResult.result\n                }\n            };\n            onMessageSent(toolResultMessage);\n            // Send the tool result back to Ollama for final response\n            const finalResponse = await fetch('/api/ollama', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    model: selectedModel,\n                    messages: [\n                        ...currentMessages,\n                        toolResultMessage\n                    ].map((msg)=>({\n                            role: msg.role,\n                            content: msg.content,\n                            ...msg.toolCall && {\n                                tool_calls: [\n                                    msg.toolCall\n                                ]\n                            }\n                        })),\n                    tools: availableTools\n                })\n            });\n            if (finalResponse.ok) {\n                const finalData = await finalResponse.json();\n                const finalMessage = {\n                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(),\n                    role: 'assistant',\n                    content: finalData.message.content,\n                    timestamp: new Date()\n                };\n                onMessageSent(finalMessage);\n            }\n        } catch (err) {\n            console.error('Error executing tool call:', err);\n            // Add error message\n            const errorMessage = {\n                id: (0,uuid__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(),\n                role: 'assistant',\n                content: 'Sorry, I encountered an error while using the tool. Please try again.',\n                timestamp: new Date()\n            };\n            onMessageSent(errorMessage);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n        display: \"flex\",\n        flexDirection: \"column\",\n        height: \"100%\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                children: error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: -20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        y: -20\n                    },\n                    transition: {\n                        duration: 0.3\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        sx: {\n                            p: 2\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            severity: \"error\",\n                            onClose: ()=>setError(null),\n                            sx: {\n                                borderRadius: 2,\n                                backdropFilter: 'blur(20px)',\n                                background: 'rgba(255, 68, 68, 0.1)',\n                                border: '1px solid rgba(255, 68, 68, 0.3)',\n                                color: '#ff4444',\n                                '& .MuiAlert-icon': {\n                                    color: '#ff4444'\n                                }\n                            },\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                    lineNumber: 288,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                lineNumber: 286,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                flex: 1,\n                overflow: \"auto\",\n                sx: {\n                    px: 3,\n                    py: 2\n                },\n                children: [\n                    messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.5\n                        },\n                        style: {\n                            height: '100%'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            display: \"flex\",\n                            flexDirection: \"column\",\n                            alignItems: \"center\",\n                            justifyContent: \"center\",\n                            height: \"100%\",\n                            textAlign: \"center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    variant: \"h6\",\n                                    sx: {\n                                        color: 'rgba(255, 255, 255, 0.8)',\n                                        mb: 1,\n                                        fontWeight: 500\n                                    },\n                                    children: conversation ? 'Start chatting' : 'Select a conversation'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    variant: \"body2\",\n                                    sx: {\n                                        color: 'rgba(255, 255, 255, 0.6)',\n                                        maxWidth: 400\n                                    },\n                                    children: conversation ? 'Type a message to begin the conversation with your AI assistant' : 'Choose a conversation from the sidebar or create a new one to get started'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                                    lineNumber: 343,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                        children: messages.map((message, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: index * 0.1,\n                                    duration: 0.3\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MessageBubble__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    message: message,\n                                    isUser: message.role === 'user'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 17\n                                }, this)\n                            }, message.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                        lineNumber: 358,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                        children: loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            exit: {\n                                opacity: 0,\n                                y: -20\n                            },\n                            transition: {\n                                duration: 0.3\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TypingIndicator__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                                lineNumber: 384,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                            lineNumber: 378,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                        lineNumber: 376,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                        lineNumber: 389,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                lineNumber: 317,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                sx: {\n                    p: 3,\n                    borderTop: '1px solid rgba(255, 255, 255, 0.1)'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InputBar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    onSendMessage: handleSendMessage,\n                    disabled: !selectedModel || !conversation,\n                    loading: loading,\n                    modelCapabilities: modelCapabilities\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                    lineNumber: 394,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                lineNumber: 393,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n        lineNumber: 284,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatWindow, \"yBUK5kIkV3sR5SA/URI0pxGhTuo=\", false, function() {\n    return [\n        _barrel_optimize_names_Alert_Box_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n});\n_c = ChatWindow;\nvar _c;\n$RefreshReg$(_c, \"ChatWindow\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/ChatWindow/ChatWindow.tsx\n"));

/***/ })

});