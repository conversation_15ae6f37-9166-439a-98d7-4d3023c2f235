'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typo<PERSON>,
  Button,
  IconButton,
  Chip,
  Stack,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Tooltip,
  CircularProgress,
  Divider,
  useTheme,
  alpha,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  PlayArrow as PlayIcon,
  Stop as StopIcon,
  Settings as SettingsIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Build as BuildIcon,
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { MCPConfig, MCPServerConfig } from '@/types';

interface MCPServerManagerProps {
  config: MCPConfig;
  onConfigUpdate: (config: MCPConfig) => void;
  availableTools: any[];
}

interface ServerStatus {
  [serverName: string]: {
    running: boolean;
    tools: number;
    error?: string;
  };
}

export default function MCPServerManager({
  config,
  onConfigUpdate,
  availableTools,
}: MCPServerManagerProps) {
  const [open, setOpen] = useState(false);
  const [serverStatus, setServerStatus] = useState<ServerStatus>({});
  const [loading, setLoading] = useState<string | null>(null);
  const [newServerDialog, setNewServerDialog] = useState(false);
  const [newServerName, setNewServerName] = useState('');
  const [newServerCommand, setNewServerCommand] = useState('npx');
  const [newServerArgs, setNewServerArgs] = useState('');
  const [newServerDescription, setNewServerDescription] = useState('');
  const theme = useTheme();

  useEffect(() => {
    // Initialize server status
    const status: ServerStatus = {};
    Object.keys(config.mcpServers || {}).forEach(serverName => {
      const serverTools = availableTools.filter(tool => tool.serverName === serverName);
      status[serverName] = {
        running: serverTools.length > 0,
        tools: serverTools.length,
      };
    });
    setServerStatus(status);
  }, [config, availableTools]);

  const handleStartServer = async (serverName: string) => {
    setLoading(serverName);
    try {
      const response = await fetch('/api/mcp', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'start_server',
          serverName,
        }),
      });

      if (response.ok) {
        setServerStatus(prev => ({
          ...prev,
          [serverName]: { ...prev[serverName], running: true, error: undefined },
        }));
      } else {
        const error = await response.json();
        setServerStatus(prev => ({
          ...prev,
          [serverName]: { ...prev[serverName], running: false, error: error.error },
        }));
      }
    } catch (error) {
      setServerStatus(prev => ({
        ...prev,
        [serverName]: { ...prev[serverName], running: false, error: 'Failed to start server' },
      }));
    } finally {
      setLoading(null);
    }
  };

  const handleStopServer = async (serverName: string) => {
    setLoading(serverName);
    try {
      const response = await fetch('/api/mcp', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'stop_server',
          serverName,
        }),
      });

      if (response.ok) {
        setServerStatus(prev => ({
          ...prev,
          [serverName]: { ...prev[serverName], running: false, error: undefined },
        }));
      }
    } catch (error) {
      console.error('Failed to stop server:', error);
    } finally {
      setLoading(null);
    }
  };

  const handleAddServer = () => {
    if (!newServerName || !newServerArgs) return;

    const args = newServerArgs.split(' ').filter(arg => arg.trim());
    const newConfig = {
      ...config,
      mcpServers: {
        ...config.mcpServers,
        [newServerName]: {
          command: newServerCommand,
          args,
          description: newServerDescription || undefined,
        },
      },
    };

    onConfigUpdate(newConfig);
    setNewServerDialog(false);
    setNewServerName('');
    setNewServerArgs('');
    setNewServerDescription('');
  };

  const handleRemoveServer = (serverName: string) => {
    const newConfig = { ...config };
    delete newConfig.mcpServers[serverName];
    onConfigUpdate(newConfig);
  };

  return (
    <>
      <Button
        variant="outlined"
        startIcon={<SettingsIcon />}
        onClick={() => setOpen(true)}
        sx={{
          borderRadius: 2,
          textTransform: 'none',
          '&:hover': {
            backgroundColor: alpha(theme.palette.primary.main, 0.1),
          },
        }}
      >
        MCP Servers
      </Button>

      <Dialog
        open={open}
        onClose={() => setOpen(false)}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 3,
            background: alpha(theme.palette.background.paper, 0.95),
            backdropFilter: 'blur(20px)',
          },
        }}
      >
        <DialogTitle>
          <Stack direction="row" alignItems="center" justifyContent="space-between">
            <Typography variant="h6">MCP Server Management</Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => setNewServerDialog(true)}
              size="small"
              sx={{ borderRadius: 2 }}
            >
              Add Server
            </Button>
          </Stack>
        </DialogTitle>

        <DialogContent>
          <Stack spacing={2}>
            {Object.entries(config.mcpServers || {}).map(([serverName, serverConfig]) => {
              const status = serverStatus[serverName] || { running: false, tools: 0 };
              const isLoading = loading === serverName;

              return (
                <motion.div
                  key={serverName}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  <Paper
                    elevation={2}
                    sx={{
                      p: 3,
                      borderRadius: 2,
                      border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                      background: alpha(theme.palette.background.paper, 0.8),
                    }}
                  >
                    <Stack direction="row" alignItems="center" justifyContent="space-between" mb={2}>
                      <Stack direction="row" alignItems="center" spacing={2}>
                        <Typography variant="h6">{serverName}</Typography>
                        <Chip
                          icon={status.running ? <CheckCircleIcon /> : <ErrorIcon />}
                          label={status.running ? 'Running' : 'Stopped'}
                          color={status.running ? 'success' : 'error'}
                          size="small"
                        />
                        {status.tools > 0 && (
                          <Chip
                            icon={<BuildIcon />}
                            label={`${status.tools} tools`}
                            color="primary"
                            variant="outlined"
                            size="small"
                          />
                        )}
                      </Stack>

                      <Stack direction="row" spacing={1}>
                        <Tooltip title={status.running ? 'Stop Server' : 'Start Server'}>
                          <IconButton
                            onClick={() => status.running ? handleStopServer(serverName) : handleStartServer(serverName)}
                            disabled={isLoading}
                            color={status.running ? 'error' : 'success'}
                          >
                            {isLoading ? (
                              <CircularProgress size={20} />
                            ) : status.running ? (
                              <StopIcon />
                            ) : (
                              <PlayIcon />
                            )}
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Remove Server">
                          <IconButton
                            onClick={() => handleRemoveServer(serverName)}
                            color="error"
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Tooltip>
                      </Stack>
                    </Stack>

                    <Typography variant="body2" color="text.secondary" mb={1}>
                      {serverConfig.description || 'No description'}
                    </Typography>

                    <Typography variant="caption" color="text.secondary">
                      Command: {serverConfig.command} {serverConfig.args.join(' ')}
                    </Typography>

                    {status.error && (
                      <Alert severity="error" sx={{ mt: 2 }}>
                        {status.error}
                      </Alert>
                    )}
                  </Paper>
                </motion.div>
              );
            })}
          </Stack>
        </DialogContent>

        <DialogActions>
          <Button onClick={() => setOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Add Server Dialog */}
      <Dialog
        open={newServerDialog}
        onClose={() => setNewServerDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Add New MCP Server</DialogTitle>
        <DialogContent>
          <Stack spacing={3} sx={{ mt: 1 }}>
            <TextField
              label="Server Name"
              value={newServerName}
              onChange={(e) => setNewServerName(e.target.value)}
              fullWidth
              required
            />
            <FormControl fullWidth>
              <InputLabel>Command</InputLabel>
              <Select
                value={newServerCommand}
                onChange={(e) => setNewServerCommand(e.target.value)}
                label="Command"
              >
                <MenuItem value="npx">npx</MenuItem>
                <MenuItem value="node">node</MenuItem>
                <MenuItem value="python">python</MenuItem>
                <MenuItem value="bunx">bunx</MenuItem>
              </Select>
            </FormControl>
            <TextField
              label="Arguments"
              value={newServerArgs}
              onChange={(e) => setNewServerArgs(e.target.value)}
              fullWidth
              required
              placeholder="-y @upstash/context7-mcp"
              helperText="Space-separated arguments"
            />
            <TextField
              label="Description"
              value={newServerDescription}
              onChange={(e) => setNewServerDescription(e.target.value)}
              fullWidth
              multiline
              rows={2}
            />
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setNewServerDialog(false)}>Cancel</Button>
          <Button
            onClick={handleAddServer}
            variant="contained"
            disabled={!newServerName || !newServerArgs}
          >
            Add Server
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}
