"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/components/ChatWindow/ChatWindow.tsx":
/*!**************************************************!*\
  !*** ./app/components/ChatWindow/ChatWindow.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatWindow)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _MessageBubble__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MessageBubble */ \"(app-pages-browser)/./app/components/ChatWindow/MessageBubble.tsx\");\n/* harmony import */ var _InputBar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./InputBar */ \"(app-pages-browser)/./app/components/ChatWindow/InputBar.tsx\");\n/* harmony import */ var _TypingIndicator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../TypingIndicator */ \"(app-pages-browser)/./app/components/TypingIndicator.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ChatWindow(param) {\n    let { selectedModel, availableTools, conversation, onMessageSent, modelCapabilities } = param;\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const messages = (conversation === null || conversation === void 0 ? void 0 : conversation.messages) || [];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatWindow.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ChatWindow.useEffect\"], [\n        messages\n    ]);\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: 'smooth'\n        });\n    };\n    const handleSendMessage = async (content, attachments)=>{\n        if (!selectedModel) {\n            setError('Please select a model first');\n            return;\n        }\n        // Create user message\n        const userMessage = {\n            id: (0,uuid__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(),\n            role: 'user',\n            content,\n            timestamp: new Date(),\n            attachments\n        };\n        onMessageSent(userMessage);\n        setLoading(true);\n        setError(null);\n        try {\n            // Check if message has vision content\n            const hasVisionContent = attachments === null || attachments === void 0 ? void 0 : attachments.some((att)=>att.type === 'image');\n            // Validate vision capability\n            if (hasVisionContent && !(modelCapabilities === null || modelCapabilities === void 0 ? void 0 : modelCapabilities.supportsVision)) {\n                setError(\"Model \".concat(selectedModel, \" does not support vision/image processing. Please select a vision-capable model like llava or qwen2-vl.\"));\n                setLoading(false);\n                return;\n            }\n            // Prepare messages for API\n            const conversationMessages = [\n                ...messages,\n                userMessage\n            ];\n            // Only include tools if model supports them\n            const toolsToSend = (modelCapabilities === null || modelCapabilities === void 0 ? void 0 : modelCapabilities.supportsTools) ? availableTools : [];\n            console.log('ChatWindow - Available tools:', availableTools);\n            console.log('ChatWindow - Model capabilities:', modelCapabilities);\n            console.log('ChatWindow - Tools to send:', toolsToSend);\n            // Send to Ollama API\n            const response = await fetch('/api/ollama', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    model: selectedModel,\n                    messages: conversationMessages.map((msg)=>({\n                            role: msg.role,\n                            content: msg.content,\n                            ...msg.toolCall && {\n                                tool_calls: [\n                                    msg.toolCall\n                                ]\n                            }\n                        })),\n                    tools: toolsToSend,\n                    hasVisionContent\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to get response from Ollama');\n            }\n            const data = await response.json();\n            const assistantMessage = data.message;\n            console.log('Received assistant message:', assistantMessage);\n            console.log('Model capabilities:', modelCapabilities);\n            // Create assistant message\n            const newAssistantMessage = {\n                id: (0,uuid__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(),\n                role: 'assistant',\n                content: assistantMessage.content,\n                timestamp: new Date()\n            };\n            // Check if there are tool calls (only if model supports tools)\n            if ((modelCapabilities === null || modelCapabilities === void 0 ? void 0 : modelCapabilities.supportsTools) && assistantMessage.tool_calls && assistantMessage.tool_calls.length > 0) {\n                console.log('Tool calls detected:', assistantMessage.tool_calls);\n                const toolCall = assistantMessage.tool_calls[0];\n                // Add an ID to the tool call if it doesn't have one\n                if (!toolCall.id) {\n                    toolCall.id = (0,uuid__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n                }\n                newAssistantMessage.toolCall = toolCall;\n                // Add the assistant message with tool call\n                onMessageSent(newAssistantMessage);\n                // Execute the tool call\n                await executeToolCall(toolCall, [\n                    ...messages,\n                    userMessage,\n                    newAssistantMessage\n                ]);\n            } else {\n                // Add the assistant message\n                onMessageSent(newAssistantMessage);\n            }\n        } catch (err) {\n            console.error('Error sending message:', err);\n            setError('Failed to send message. Please try again.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const executeToolCall = async (toolCall, currentMessages)=>{\n        try {\n            console.log('Executing tool call:', toolCall);\n            // Parse tool arguments\n            let toolArgs;\n            try {\n                toolArgs = typeof toolCall.function.arguments === 'string' ? JSON.parse(toolCall.function.arguments) : toolCall.function.arguments;\n            } catch (parseError) {\n                console.error('Failed to parse tool arguments:', parseError);\n                toolArgs = toolCall.function.arguments;\n            }\n            console.log('Tool arguments:', toolArgs);\n            // Determine server name based on tool name\n            let serverName = 'exa'; // default\n            if (toolCall.function.name === 'get_weather') {\n                serverName = 'weather';\n            } else if (toolCall.function.name === 'search_web') {\n                serverName = 'exa';\n            }\n            console.log(\"Using server: \".concat(serverName, \" for tool: \").concat(toolCall.function.name));\n            // Execute tool via MCP API\n            const response = await fetch('/api/mcp', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'execute_tool',\n                    serverName: serverName,\n                    toolName: toolCall.function.name,\n                    arguments: toolArgs\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to execute tool');\n            }\n            const toolResult = await response.json();\n            // Create tool result message\n            const toolResultMessage = {\n                id: (0,uuid__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(),\n                role: 'tool',\n                content: JSON.stringify(toolResult.result, null, 2),\n                timestamp: new Date(),\n                toolResult: {\n                    toolCallId: toolCall.id,\n                    result: toolResult.result\n                }\n            };\n            onMessageSent(toolResultMessage);\n            // Send the tool result back to Ollama for final response\n            const finalResponse = await fetch('/api/ollama', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    model: selectedModel,\n                    messages: [\n                        ...currentMessages,\n                        toolResultMessage\n                    ].map((msg)=>({\n                            role: msg.role,\n                            content: msg.content,\n                            ...msg.toolCall && {\n                                tool_calls: [\n                                    msg.toolCall\n                                ]\n                            }\n                        })),\n                    tools: availableTools\n                })\n            });\n            if (finalResponse.ok) {\n                const finalData = await finalResponse.json();\n                const finalMessage = {\n                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(),\n                    role: 'assistant',\n                    content: finalData.message.content,\n                    timestamp: new Date()\n                };\n                onMessageSent(finalMessage);\n            }\n        } catch (err) {\n            console.error('Error executing tool call:', err);\n            // Add error message\n            const errorMessage = {\n                id: (0,uuid__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(),\n                role: 'assistant',\n                content: 'Sorry, I encountered an error while using the tool. Please try again.',\n                timestamp: new Date()\n            };\n            onMessageSent(errorMessage);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        display: \"flex\",\n        flexDirection: \"column\",\n        height: \"100%\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                children: error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: -20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        y: -20\n                    },\n                    transition: {\n                        duration: 0.3\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        sx: {\n                            p: 2\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            severity: \"error\",\n                            onClose: ()=>setError(null),\n                            sx: {\n                                borderRadius: 2,\n                                backdropFilter: 'blur(20px)',\n                                background: 'rgba(255, 68, 68, 0.1)',\n                                border: '1px solid rgba(255, 68, 68, 0.3)',\n                                color: '#ff4444',\n                                '& .MuiAlert-icon': {\n                                    color: '#ff4444'\n                                }\n                            },\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                    lineNumber: 276,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                lineNumber: 274,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                flex: 1,\n                overflow: \"auto\",\n                sx: {\n                    px: 3,\n                    py: 2\n                },\n                children: [\n                    messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.5\n                        },\n                        style: {\n                            height: '100%'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            display: \"flex\",\n                            flexDirection: \"column\",\n                            alignItems: \"center\",\n                            justifyContent: \"center\",\n                            height: \"100%\",\n                            textAlign: \"center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    variant: \"h6\",\n                                    sx: {\n                                        color: 'rgba(255, 255, 255, 0.8)',\n                                        mb: 1,\n                                        fontWeight: 500\n                                    },\n                                    children: conversation ? 'Start chatting' : 'Select a conversation'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    variant: \"body2\",\n                                    sx: {\n                                        color: 'rgba(255, 255, 255, 0.6)',\n                                        maxWidth: 400\n                                    },\n                                    children: conversation ? 'Type a message to begin the conversation with your AI assistant' : 'Choose a conversation from the sidebar or create a new one to get started'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                        lineNumber: 307,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                        children: messages.map((message, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: index * 0.1,\n                                    duration: 0.3\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MessageBubble__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    message: message,\n                                    isUser: message.role === 'user'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 17\n                                }, this)\n                            }, message.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                        lineNumber: 346,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                        children: loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            exit: {\n                                opacity: 0,\n                                y: -20\n                            },\n                            transition: {\n                                duration: 0.3\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TypingIndicator__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                            lineNumber: 366,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                        lineNumber: 364,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                        lineNumber: 377,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                lineNumber: 305,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                sx: {\n                    p: 3,\n                    borderTop: '1px solid rgba(255, 255, 255, 0.1)'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InputBar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    onSendMessage: handleSendMessage,\n                    disabled: !selectedModel || !conversation,\n                    loading: loading,\n                    modelCapabilities: modelCapabilities\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                    lineNumber: 382,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                lineNumber: 381,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n        lineNumber: 272,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatWindow, \"EYjj5BViCHitUwPd0M3b8vWEXZg=\");\n_c = ChatWindow;\nvar _c;\n$RefreshReg$(_c, \"ChatWindow\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/ChatWindow/ChatWindow.tsx\n"));

/***/ })

});