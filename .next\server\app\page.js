/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp%5Ccomponents%5CAnimatedBackground.tsx&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp%5Ccomponents%5CThemeProvider.tsx&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp%5Ccomponents%5CAnimatedBackground.tsx&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp%5Ccomponents%5CThemeProvider.tsx&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/AnimatedBackground.tsx */ \"(ssr)/./app/components/AnimatedBackground.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/ThemeProvider.tsx */ \"(ssr)/./app/components/ThemeProvider.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDMjAwMjE3OTIlNUNEb2N1bWVudHMlNUNQcm9qZWN0cyU1Q0NoYXQlNUNhcHAlNUNjb21wb25lbnRzJTVDQW5pbWF0ZWRCYWNrZ3JvdW5kLnRzeCZtb2R1bGVzPUMlM0ElNUNVc2VycyU1QzIwMDIxNzkyJTVDRG9jdW1lbnRzJTVDUHJvamVjdHMlNUNDaGF0JTVDYXBwJTVDY29tcG9uZW50cyU1Q1RoZW1lUHJvdmlkZXIudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwTEFBNEg7QUFDNUgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vbGxhbWEtbWNwLWNoYXQvP2ZhOGMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFwyMDAyMTc5MlxcXFxEb2N1bWVudHNcXFxcUHJvamVjdHNcXFxcQ2hhdFxcXFxhcHBcXFxcY29tcG9uZW50c1xcXFxBbmltYXRlZEJhY2tncm91bmQudHN4XCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFwyMDAyMTc5MlxcXFxEb2N1bWVudHNcXFxcUHJvamVjdHNcXFxcQ2hhdFxcXFxhcHBcXFxcY29tcG9uZW50c1xcXFxUaGVtZVByb3ZpZGVyLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp%5Ccomponents%5CAnimatedBackground.tsx&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp%5Ccomponents%5CThemeProvider.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp%5Cpage.tsx&server=true!":
/*!*************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp%5Cpage.tsx&server=true! ***!
  \*************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDMjAwMjE3OTIlNUNEb2N1bWVudHMlNUNQcm9qZWN0cyU1Q0NoYXQlNUNhcHAlNUNwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vbGxhbWEtbWNwLWNoYXQvPzU4NjMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFwyMDAyMTc5MlxcXFxEb2N1bWVudHNcXFxcUHJvamVjdHNcXFxcQ2hhdFxcXFxhcHBcXFxccGFnZS50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/components/AnimatedBackground.tsx":
/*!***********************************************!*\
  !*** ./app/components/AnimatedBackground.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnimatedBackground)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Box_mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Box!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction AnimatedBackground() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        sx: {\n            position: \"fixed\",\n            top: 0,\n            left: 0,\n            width: \"100%\",\n            height: \"100%\",\n            zIndex: -1,\n            overflow: \"hidden\",\n            background: \"linear-gradient(135deg, #000000 0%, #1a1a1a 50%, #000000 100%)\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                style: {\n                    position: \"absolute\",\n                    top: \"10%\",\n                    left: \"10%\",\n                    width: \"300px\",\n                    height: \"300px\",\n                    borderRadius: \"50%\",\n                    background: \"radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%)\",\n                    filter: \"blur(40px)\"\n                },\n                animate: {\n                    x: [\n                        0,\n                        100,\n                        0\n                    ],\n                    y: [\n                        0,\n                        -50,\n                        0\n                    ],\n                    scale: [\n                        1,\n                        1.2,\n                        1\n                    ]\n                },\n                transition: {\n                    duration: 20,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\AnimatedBackground.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                style: {\n                    position: \"absolute\",\n                    top: \"60%\",\n                    right: \"10%\",\n                    width: \"400px\",\n                    height: \"400px\",\n                    borderRadius: \"50%\",\n                    background: \"radial-gradient(circle, rgba(255, 255, 255, 0.05) 0%, transparent 70%)\",\n                    filter: \"blur(40px)\"\n                },\n                animate: {\n                    x: [\n                        0,\n                        -80,\n                        0\n                    ],\n                    y: [\n                        0,\n                        60,\n                        0\n                    ],\n                    scale: [\n                        1,\n                        0.8,\n                        1\n                    ]\n                },\n                transition: {\n                    duration: 25,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\AnimatedBackground.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                style: {\n                    position: \"absolute\",\n                    bottom: \"20%\",\n                    left: \"30%\",\n                    width: \"250px\",\n                    height: \"250px\",\n                    borderRadius: \"50%\",\n                    background: \"radial-gradient(circle, rgba(255, 255, 255, 0.08) 0%, transparent 70%)\",\n                    filter: \"blur(30px)\"\n                },\n                animate: {\n                    x: [\n                        0,\n                        60,\n                        0\n                    ],\n                    y: [\n                        0,\n                        -40,\n                        0\n                    ],\n                    scale: [\n                        1,\n                        1.3,\n                        1\n                    ]\n                },\n                transition: {\n                    duration: 18,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\AnimatedBackground.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            Array.from({\n                length: 20\n            }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    style: {\n                        position: \"absolute\",\n                        width: \"4px\",\n                        height: \"4px\",\n                        borderRadius: \"50%\",\n                        background: \"rgba(255, 255, 255, 0.2)\",\n                        left: `${Math.random() * 100}%`,\n                        top: `${Math.random() * 100}%`\n                    },\n                    animate: {\n                        y: [\n                            0,\n                            -100,\n                            0\n                        ],\n                        opacity: [\n                            0,\n                            1,\n                            0\n                        ]\n                    },\n                    transition: {\n                        duration: 3 + Math.random() * 2,\n                        repeat: Infinity,\n                        delay: Math.random() * 2,\n                        ease: \"easeInOut\"\n                    }\n                }, i, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\AnimatedBackground.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, this))\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\AnimatedBackground.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/AnimatedBackground.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/ChatWindow/ChatWindow.tsx":
/*!**************************************************!*\
  !*** ./app/components/ChatWindow/ChatWindow.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatWindow)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! uuid */ \"(ssr)/./node_modules/uuid/dist/esm-node/v4.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _MessageBubble__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MessageBubble */ \"(ssr)/./app/components/ChatWindow/MessageBubble.tsx\");\n/* harmony import */ var _InputBar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./InputBar */ \"(ssr)/./app/components/ChatWindow/InputBar.tsx\");\n/* harmony import */ var _TypingIndicator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../TypingIndicator */ \"(ssr)/./app/components/TypingIndicator.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction ChatWindow({ selectedModel, availableTools, conversation, onMessageSent, modelCapabilities }) {\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const messages = conversation?.messages || [];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        scrollToBottom();\n    }, [\n        messages\n    ]);\n    const scrollToBottom = ()=>{\n        messagesEndRef.current?.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    const handleSendMessage = async (content, attachments)=>{\n        if (!selectedModel) {\n            setError(\"Please select a model first\");\n            return;\n        }\n        // Create user message\n        const userMessage = {\n            id: (0,uuid__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(),\n            role: \"user\",\n            content,\n            timestamp: new Date(),\n            attachments\n        };\n        onMessageSent(userMessage);\n        setLoading(true);\n        setError(null);\n        try {\n            // Check if message has vision content\n            const hasVisionContent = attachments?.some((att)=>att.type === \"image\");\n            // Validate vision capability\n            if (hasVisionContent && !modelCapabilities?.supportsVision) {\n                setError(`Model ${selectedModel} does not support vision/image processing. Please select a vision-capable model like llava or qwen2-vl.`);\n                setLoading(false);\n                return;\n            }\n            // Prepare messages for API\n            const conversationMessages = [\n                ...messages,\n                userMessage\n            ];\n            // Only include tools if model supports them\n            const toolsToSend = modelCapabilities?.supportsTools ? availableTools : [];\n            console.log(\"ChatWindow - Available tools:\", availableTools);\n            console.log(\"ChatWindow - Model capabilities:\", modelCapabilities);\n            console.log(\"ChatWindow - Tools to send:\", toolsToSend);\n            // Send to Ollama API\n            const response = await fetch(\"/api/ollama\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    model: selectedModel,\n                    messages: conversationMessages.map((msg)=>({\n                            role: msg.role,\n                            content: msg.content,\n                            ...msg.toolCall && {\n                                tool_calls: [\n                                    msg.toolCall\n                                ]\n                            }\n                        })),\n                    tools: toolsToSend,\n                    hasVisionContent\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to get response from Ollama\");\n            }\n            const data = await response.json();\n            const assistantMessage = data.message;\n            console.log(\"Received assistant message:\", assistantMessage);\n            console.log(\"Model capabilities:\", modelCapabilities);\n            // Create assistant message\n            const newAssistantMessage = {\n                id: (0,uuid__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(),\n                role: \"assistant\",\n                content: assistantMessage.content,\n                timestamp: new Date()\n            };\n            // Check if there are tool calls (only if model supports tools)\n            if (modelCapabilities?.supportsTools && assistantMessage.tool_calls && assistantMessage.tool_calls.length > 0) {\n                console.log(\"Tool calls detected:\", assistantMessage.tool_calls);\n                const toolCall = assistantMessage.tool_calls[0];\n                // Add an ID to the tool call if it doesn't have one\n                if (!toolCall.id) {\n                    toolCall.id = (0,uuid__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n                }\n                newAssistantMessage.toolCall = toolCall;\n                // Add the assistant message with tool call\n                onMessageSent(newAssistantMessage);\n                // Execute the tool call\n                await executeToolCall(toolCall, [\n                    ...messages,\n                    userMessage,\n                    newAssistantMessage\n                ]);\n            } else {\n                // Add the assistant message\n                onMessageSent(newAssistantMessage);\n            }\n        } catch (err) {\n            console.error(\"Error sending message:\", err);\n            setError(\"Failed to send message. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const executeToolCall = async (toolCall, currentMessages)=>{\n        try {\n            console.log(\"Executing tool call:\", toolCall);\n            // Parse tool arguments\n            let toolArgs;\n            try {\n                toolArgs = typeof toolCall.function.arguments === \"string\" ? JSON.parse(toolCall.function.arguments) : toolCall.function.arguments;\n            } catch (parseError) {\n                console.error(\"Failed to parse tool arguments:\", parseError);\n                toolArgs = toolCall.function.arguments;\n            }\n            console.log(\"Tool arguments:\", toolArgs);\n            // Determine server name based on tool name\n            let serverName = \"exa\"; // default\n            if (toolCall.function.name === \"get_weather\") {\n                serverName = \"weather\";\n            } else if (toolCall.function.name === \"search_web\") {\n                serverName = \"exa\";\n            }\n            console.log(`Using server: ${serverName} for tool: ${toolCall.function.name}`);\n            // Execute tool via MCP API\n            const response = await fetch(\"/api/mcp\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    action: \"execute_tool\",\n                    serverName: serverName,\n                    toolName: toolCall.function.name,\n                    arguments: toolArgs\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to execute tool\");\n            }\n            const toolResult = await response.json();\n            // Create tool result message\n            const toolResultMessage = {\n                id: (0,uuid__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(),\n                role: \"tool\",\n                content: JSON.stringify(toolResult.result, null, 2),\n                timestamp: new Date(),\n                toolResult: {\n                    toolCallId: toolCall.id,\n                    result: toolResult.result\n                }\n            };\n            onMessageSent(toolResultMessage);\n            // Send the tool result back to Ollama for final response\n            const finalResponse = await fetch(\"/api/ollama\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    model: selectedModel,\n                    messages: [\n                        ...currentMessages,\n                        toolResultMessage\n                    ].map((msg)=>({\n                            role: msg.role,\n                            content: msg.content,\n                            ...msg.toolCall && {\n                                tool_calls: [\n                                    msg.toolCall\n                                ]\n                            }\n                        })),\n                    tools: availableTools\n                })\n            });\n            if (finalResponse.ok) {\n                const finalData = await finalResponse.json();\n                const finalMessage = {\n                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(),\n                    role: \"assistant\",\n                    content: finalData.message.content,\n                    timestamp: new Date()\n                };\n                onMessageSent(finalMessage);\n            }\n        } catch (err) {\n            console.error(\"Error executing tool call:\", err);\n            // Add error message\n            const errorMessage = {\n                id: (0,uuid__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(),\n                role: \"assistant\",\n                content: \"Sorry, I encountered an error while using the tool. Please try again.\",\n                timestamp: new Date()\n            };\n            onMessageSent(errorMessage);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        display: \"flex\",\n        flexDirection: \"column\",\n        height: \"100%\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                children: error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: -20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        y: -20\n                    },\n                    transition: {\n                        duration: 0.3\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        sx: {\n                            p: 2\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            severity: \"error\",\n                            onClose: ()=>setError(null),\n                            sx: {\n                                borderRadius: 2,\n                                backdropFilter: \"blur(20px)\",\n                                background: \"rgba(255, 68, 68, 0.1)\",\n                                border: \"1px solid rgba(255, 68, 68, 0.3)\",\n                                color: \"#ff4444\",\n                                \"& .MuiAlert-icon\": {\n                                    color: \"#ff4444\"\n                                }\n                            },\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                            lineNumber: 281,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                    lineNumber: 274,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                lineNumber: 272,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                flex: 1,\n                overflow: \"auto\",\n                sx: {\n                    px: 3,\n                    py: 2\n                },\n                children: [\n                    messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.5\n                        },\n                        style: {\n                            height: \"100%\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            display: \"flex\",\n                            flexDirection: \"column\",\n                            alignItems: \"center\",\n                            justifyContent: \"center\",\n                            height: \"100%\",\n                            textAlign: \"center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    variant: \"h6\",\n                                    sx: {\n                                        color: \"rgba(255, 255, 255, 0.8)\",\n                                        mb: 1,\n                                        fontWeight: 500\n                                    },\n                                    children: conversation ? \"Start chatting\" : \"Select a conversation\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    variant: \"body2\",\n                                    sx: {\n                                        color: \"rgba(255, 255, 255, 0.6)\",\n                                        maxWidth: 400\n                                    },\n                                    children: conversation ? \"Type a message to begin the conversation with your AI assistant\" : \"Choose a conversation from the sidebar or create a new one to get started\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                        lineNumber: 305,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                        children: messages.map((message, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: index * 0.1,\n                                    duration: 0.3\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MessageBubble__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    message: message,\n                                    isUser: message.role === \"user\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 17\n                                }, this)\n                            }, message.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                        children: loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            exit: {\n                                opacity: 0,\n                                y: -20\n                            },\n                            transition: {\n                                duration: 0.3\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TypingIndicator__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                                lineNumber: 370,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                            lineNumber: 364,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                        lineNumber: 362,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                        lineNumber: 375,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                lineNumber: 303,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                sx: {\n                    p: 3,\n                    borderTop: \"1px solid rgba(255, 255, 255, 0.1)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InputBar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    onSendMessage: handleSendMessage,\n                    disabled: !selectedModel || !conversation,\n                    loading: loading,\n                    modelCapabilities: modelCapabilities\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                    lineNumber: 380,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                lineNumber: 379,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n        lineNumber: 270,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ChatWindow/ChatWindow.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/ChatWindow/InputBar.tsx":
/*!************************************************!*\
  !*** ./app/components/ChatWindow/InputBar.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ InputBar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Box_Button_Chip_CircularProgress_IconButton_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Chip,CircularProgress,IconButton,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Chip_CircularProgress_IconButton_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Chip,CircularProgress,IconButton,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Chip_CircularProgress_IconButton_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Chip,CircularProgress,IconButton,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Chip_CircularProgress_IconButton_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Chip,CircularProgress,IconButton,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Tooltip/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Chip_CircularProgress_IconButton_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Chip,CircularProgress,IconButton,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Chip_CircularProgress_IconButton_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Chip,CircularProgress,IconButton,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Chip_CircularProgress_IconButton_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Chip,CircularProgress,IconButton,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Chip_CircularProgress_IconButton_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Chip,CircularProgress,IconButton,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_AttachFile_Close_CloudUpload_Description_Image_Send_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AttachFile,Close,CloudUpload,Description,Image,Send!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/CloudUpload.js\");\n/* harmony import */ var _barrel_optimize_names_AttachFile_Close_CloudUpload_Description_Image_Send_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AttachFile,Close,CloudUpload,Description,Image,Send!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Image.js\");\n/* harmony import */ var _barrel_optimize_names_AttachFile_Close_CloudUpload_Description_Image_Send_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AttachFile,Close,CloudUpload,Description,Image,Send!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Description.js\");\n/* harmony import */ var _barrel_optimize_names_AttachFile_Close_CloudUpload_Description_Image_Send_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AttachFile,Close,CloudUpload,Description,Image,Send!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Close.js\");\n/* harmony import */ var _barrel_optimize_names_AttachFile_Close_CloudUpload_Description_Image_Send_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AttachFile,Close,CloudUpload,Description,Image,Send!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/AttachFile.js\");\n/* harmony import */ var _barrel_optimize_names_AttachFile_Close_CloudUpload_Description_Image_Send_mui_icons_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AttachFile,Close,CloudUpload,Description,Image,Send!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Send.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction InputBar({ onSendMessage, disabled, loading, modelCapabilities }) {\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [attachments, setAttachments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [uploading, setUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDragOver, setIsDragOver] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const handleSend = ()=>{\n        if (message.trim() || attachments.length > 0) {\n            onSendMessage(message.trim(), attachments);\n            setMessage(\"\");\n            setAttachments([]);\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSend();\n        }\n    };\n    const processFiles = async (files)=>{\n        if (files.length === 0) return;\n        // Check for image files and vision capability\n        const imageFiles = files.filter((file)=>file.type.startsWith(\"image/\"));\n        if (imageFiles.length > 0 && !modelCapabilities?.supportsVision) {\n            alert(\"The selected model does not support vision/image processing. Please select a vision-capable model like llava or qwen2-vl to upload images.\");\n            return;\n        }\n        setUploading(true);\n        try {\n            const uploadPromises = files.map(async (file)=>{\n                const formData = new FormData();\n                formData.append(\"file\", file);\n                const response = await fetch(\"/api/upload\", {\n                    method: \"POST\",\n                    body: formData\n                });\n                if (!response.ok) {\n                    throw new Error(`Failed to upload ${file.name}`);\n                }\n                const data = await response.json();\n                return data.file;\n            });\n            const uploadedFiles = await Promise.all(uploadPromises);\n            setAttachments((prev)=>[\n                    ...prev,\n                    ...uploadedFiles\n                ]);\n        } catch (error) {\n            console.error(\"Error uploading files:\", error);\n        // TODO: Show error toast\n        } finally{\n            setUploading(false);\n        }\n    };\n    const handleFileSelect = async (e)=>{\n        const files = Array.from(e.target.files || []);\n        await processFiles(files);\n        // Reset file input\n        if (fileInputRef.current) {\n            fileInputRef.current.value = \"\";\n        }\n    };\n    const handleDragOver = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        e.preventDefault();\n        setIsDragOver(true);\n    }, []);\n    const handleDragLeave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        e.preventDefault();\n        setIsDragOver(false);\n    }, []);\n    const handleDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (e)=>{\n        e.preventDefault();\n        setIsDragOver(false);\n        const files = Array.from(e.dataTransfer.files);\n        await processFiles(files);\n    }, [\n        modelCapabilities\n    ]);\n    const removeAttachment = (attachmentId)=>{\n        setAttachments((prev)=>prev.filter((att)=>att.id !== attachmentId));\n    };\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return \"0 Bytes\";\n        const k = 1024;\n        const sizes = [\n            \"Bytes\",\n            \"KB\",\n            \"MB\",\n            \"GB\"\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Chip_CircularProgress_IconButton_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        onDragOver: handleDragOver,\n        onDragLeave: handleDragLeave,\n        onDrop: handleDrop,\n        sx: {\n            position: \"relative\",\n            transition: \"all 0.3s ease\",\n            ...isDragOver && {\n                \"&::before\": {\n                    content: '\"\"',\n                    position: \"absolute\",\n                    top: -10,\n                    left: -10,\n                    right: -10,\n                    bottom: -10,\n                    border: \"2px dashed rgba(255, 255, 255, 0.5)\",\n                    borderRadius: 2,\n                    background: \"rgba(255, 255, 255, 0.05)\",\n                    zIndex: 1\n                }\n            }\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                children: isDragOver && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    style: {\n                        position: \"absolute\",\n                        top: -10,\n                        left: -10,\n                        right: -10,\n                        bottom: -10,\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        justifyContent: \"center\",\n                        background: \"rgba(255, 255, 255, 0.1)\",\n                        borderRadius: 8,\n                        zIndex: 2\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Chip_CircularProgress_IconButton_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        textAlign: \"center\",\n                        color: \"white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AttachFile_Close_CloudUpload_Description_Image_Send_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                sx: {\n                                    fontSize: 48,\n                                    mb: 1\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Chip_CircularProgress_IconButton_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                variant: \"h6\",\n                                children: \"Drop files here\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                children: attachments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        height: 0\n                    },\n                    animate: {\n                        opacity: 1,\n                        height: \"auto\"\n                    },\n                    exit: {\n                        opacity: 0,\n                        height: 0\n                    },\n                    transition: {\n                        duration: 0.3\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Chip_CircularProgress_IconButton_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        mb: 2,\n                        display: \"flex\",\n                        flexWrap: \"wrap\",\n                        gap: 1,\n                        children: attachments.map((attachment, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    scale: 0.8\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    scale: 1\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    scale: 0.8\n                                },\n                                transition: {\n                                    delay: index * 0.1\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Chip_CircularProgress_IconButton_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    icon: attachment.type === \"image\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AttachFile_Close_CloudUpload_Description_Image_Send_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 57\n                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AttachFile_Close_CloudUpload_Description_Image_Send_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 73\n                                    }, void 0),\n                                    label: `${attachment.name} (${formatFileSize(attachment.size)})`,\n                                    onDelete: ()=>removeAttachment(attachment.id),\n                                    deleteIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AttachFile_Close_CloudUpload_Description_Image_Send_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 33\n                                    }, void 0),\n                                    size: \"small\",\n                                    sx: {\n                                        backdropFilter: \"blur(20px)\",\n                                        background: \"rgba(255, 255, 255, 0.1)\",\n                                        border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                                        color: \"white\",\n                                        \"& .MuiChip-deleteIcon\": {\n                                            color: \"rgba(255, 255, 255, 0.7)\",\n                                            \"&:hover\": {\n                                                color: \"#ff4444\"\n                                            }\n                                        }\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 19\n                                }, this)\n                            }, attachment.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 17\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                    lineNumber: 193,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Chip_CircularProgress_IconButton_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                display: \"flex\",\n                alignItems: \"flex-end\",\n                gap: 2,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Chip_CircularProgress_IconButton_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        title: !modelCapabilities?.supportsVision ? \"Image uploads require a vision-capable model\" : \"Attach files\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            whileTap: {\n                                scale: 0.95\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Chip_CircularProgress_IconButton_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                onClick: ()=>fileInputRef.current?.click(),\n                                disabled: disabled || uploading,\n                                sx: {\n                                    backdropFilter: \"blur(20px)\",\n                                    background: \"rgba(255, 255, 255, 0.05)\",\n                                    border: \"1px solid rgba(255, 255, 255, 0.1)\",\n                                    color: \"white\",\n                                    transition: \"all 0.3s ease\",\n                                    \"&:hover\": {\n                                        background: \"rgba(255, 255, 255, 0.1)\",\n                                        borderColor: \"rgba(255, 255, 255, 0.3)\",\n                                        transform: \"translateY(-1px)\"\n                                    },\n                                    \"&:disabled\": {\n                                        opacity: 0.5\n                                    }\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                                    mode: \"wait\",\n                                    children: uploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            rotate: 0\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            rotate: 360\n                                        },\n                                        exit: {\n                                            opacity: 0\n                                        },\n                                        transition: {\n                                            duration: 0.3\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Chip_CircularProgress_IconButton_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            size: 20,\n                                            sx: {\n                                                color: \"white\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, \"loading\", false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 0.8\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            scale: 1\n                                        },\n                                        exit: {\n                                            opacity: 0,\n                                            scale: 0.8\n                                        },\n                                        transition: {\n                                            duration: 0.2\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AttachFile_Close_CloudUpload_Description_Image_Send_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, \"attach\", false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        ref: fileInputRef,\n                        type: \"file\",\n                        multiple: true,\n                        accept: modelCapabilities?.supportsVision ? \"image/*,.pdf,.txt,.md,.doc,.docx\" : \".pdf,.txt,.md,.doc,.docx\",\n                        onChange: handleFileSelect,\n                        style: {\n                            display: \"none\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Chip_CircularProgress_IconButton_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        fullWidth: true,\n                        multiline: true,\n                        maxRows: 4,\n                        value: message,\n                        onChange: (e)=>setMessage(e.target.value),\n                        onKeyPress: handleKeyPress,\n                        placeholder: \"Type your message...\",\n                        variant: \"outlined\",\n                        disabled: disabled,\n                        sx: {\n                            \"& .MuiOutlinedInput-root\": {\n                                backdropFilter: \"blur(20px)\",\n                                background: \"rgba(255, 255, 255, 0.05)\",\n                                border: \"1px solid rgba(255, 255, 255, 0.1)\",\n                                borderRadius: 3,\n                                color: \"white\",\n                                transition: \"all 0.3s ease\",\n                                \"& fieldset\": {\n                                    border: \"none\"\n                                },\n                                \"&:hover\": {\n                                    background: \"rgba(255, 255, 255, 0.08)\",\n                                    borderColor: \"rgba(255, 255, 255, 0.2)\"\n                                },\n                                \"&.Mui-focused\": {\n                                    background: \"rgba(255, 255, 255, 0.1)\",\n                                    borderColor: \"rgba(255, 255, 255, 0.3)\",\n                                    boxShadow: \"0 0 0 2px rgba(255, 255, 255, 0.1)\"\n                                }\n                            },\n                            \"& .MuiInputBase-input\": {\n                                color: \"white\",\n                                \"&::placeholder\": {\n                                    color: \"rgba(255, 255, 255, 0.5)\",\n                                    opacity: 1\n                                }\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Chip_CircularProgress_IconButton_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        title: \"Send message\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            whileTap: {\n                                scale: 0.95\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Chip_CircularProgress_IconButton_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                variant: \"contained\",\n                                onClick: handleSend,\n                                disabled: disabled || loading || !message.trim() && attachments.length === 0,\n                                sx: {\n                                    minWidth: 48,\n                                    height: 48,\n                                    borderRadius: 3,\n                                    backdropFilter: \"blur(20px)\",\n                                    background: \"linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1))\",\n                                    border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                                    color: \"white\",\n                                    transition: \"all 0.3s ease\",\n                                    \"&:hover\": {\n                                        background: \"linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.2))\",\n                                        borderColor: \"rgba(255, 255, 255, 0.4)\",\n                                        transform: \"translateY(-1px)\",\n                                        boxShadow: \"0 4px 20px rgba(255, 255, 255, 0.1)\"\n                                    },\n                                    \"&:disabled\": {\n                                        opacity: 0.5,\n                                        background: \"rgba(255, 255, 255, 0.05)\"\n                                    }\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                                    mode: \"wait\",\n                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            rotate: 0\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            rotate: 360\n                                        },\n                                        exit: {\n                                            opacity: 0\n                                        },\n                                        transition: {\n                                            duration: 0.3\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Chip_CircularProgress_IconButton_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            size: 20,\n                                            sx: {\n                                                color: \"white\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, \"loading\", false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: -10\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        exit: {\n                                            opacity: 0,\n                                            x: 10\n                                        },\n                                        transition: {\n                                            duration: 0.2\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AttachFile_Close_CloudUpload_Description_Image_Send_mui_icons_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, \"send\", false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                                        lineNumber: 390,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                                    lineNumber: 378,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                            lineNumber: 349,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                        lineNumber: 348,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                lineNumber: 234,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n        lineNumber: 138,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ChatWindow/InputBar.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/ChatWindow/MessageBubble.tsx":
/*!*****************************************************!*\
  !*** ./app/components/ChatWindow/MessageBubble.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MessageBubble)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Chip,IconButton,Tooltip,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Chip,IconButton,Tooltip,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Chip,IconButton,Tooltip,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Chip,IconButton,Tooltip,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Tooltip/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Chip,IconButton,Tooltip,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Build_Check_ContentCopy_Description_Image_mui_icons_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Build,Check,ContentCopy,Description,Image!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Image.js\");\n/* harmony import */ var _barrel_optimize_names_Build_Check_ContentCopy_Description_Image_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Build,Check,ContentCopy,Description,Image!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Description.js\");\n/* harmony import */ var _barrel_optimize_names_Build_Check_ContentCopy_Description_Image_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Build,Check,ContentCopy,Description,Image!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Build.js\");\n/* harmony import */ var _barrel_optimize_names_Build_Check_ContentCopy_Description_Image_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Build,Check,ContentCopy,Description,Image!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Check.js\");\n/* harmony import */ var _barrel_optimize_names_Build_Check_ContentCopy_Description_Image_mui_icons_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Build,Check,ContentCopy,Description,Image!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/ContentCopy.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction MessageBubble({ message, isUser }) {\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleCopyMessage = async ()=>{\n        try {\n            await navigator.clipboard.writeText(message.content);\n            setCopied(true);\n            setTimeout(()=>setCopied(false), 2000);\n        } catch (error) {\n            console.error(\"Failed to copy message:\", error);\n        }\n    };\n    const formatTimestamp = (timestamp)=>{\n        return new Intl.DateTimeFormat(\"en-US\", {\n            hour: \"2-digit\",\n            minute: \"2-digit\",\n            hour12: true\n        }).format(new Date(timestamp));\n    };\n    const renderAttachments = ()=>{\n        if (!message.attachments || message.attachments.length === 0) {\n            return null;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            display: \"flex\",\n            flexWrap: \"wrap\",\n            gap: 1,\n            mb: 1,\n            children: message.attachments.map((attachment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    icon: attachment.type === \"image\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Build_Check_ContentCopy_Description_Image_mui_icons_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 49\n                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Build_Check_ContentCopy_Description_Image_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 65\n                    }, void 0),\n                    label: attachment.name,\n                    size: \"small\",\n                    variant: \"outlined\",\n                    onClick: ()=>window.open(attachment.url, \"_blank\"),\n                    sx: {\n                        cursor: \"pointer\"\n                    }\n                }, attachment.id, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n            lineNumber: 58,\n            columnNumber: 7\n        }, this);\n    };\n    const renderToolCall = ()=>{\n        if (!message.toolCall) {\n            return null;\n        }\n        const toolArgs = JSON.parse(message.toolCall.function.arguments);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            mb: 1,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Build_Check_ContentCopy_Description_Image_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 17\n                    }, void 0),\n                    label: `Tool: ${message.toolCall.function.name}`,\n                    size: \"small\",\n                    color: \"secondary\",\n                    variant: \"outlined\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    mt: 1,\n                    p: 1,\n                    bgcolor: \"grey.100\",\n                    borderRadius: 1,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            variant: \"caption\",\n                            color: \"text.secondary\",\n                            children: \"Arguments:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            variant: \"body2\",\n                            component: \"pre\",\n                            sx: {\n                                fontSize: \"0.75rem\"\n                            },\n                            children: JSON.stringify(toolArgs, null, 2)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n            lineNumber: 82,\n            columnNumber: 7\n        }, this);\n    };\n    const renderToolResult = ()=>{\n        if (!message.toolResult) {\n            return null;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            mb: 1,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Build_Check_ContentCopy_Description_Image_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 17\n                    }, void 0),\n                    label: \"Tool Result\",\n                    size: \"small\",\n                    color: \"success\",\n                    variant: \"outlined\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    mt: 1,\n                    p: 1,\n                    bgcolor: \"success.50\",\n                    borderRadius: 1,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        variant: \"body2\",\n                        component: \"pre\",\n                        sx: {\n                            fontSize: \"0.875rem\"\n                        },\n                        children: typeof message.toolResult.result === \"string\" ? message.toolResult.result : JSON.stringify(message.toolResult.result, null, 2)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n            lineNumber: 108,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20,\n            scale: 0.95\n        },\n        animate: {\n            opacity: 1,\n            y: 0,\n            scale: 1\n        },\n        transition: {\n            duration: 0.3,\n            ease: \"easeOut\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            display: \"flex\",\n            justifyContent: isUser ? \"flex-end\" : \"flex-start\",\n            mb: 3,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                whileHover: {\n                    scale: 1.02\n                },\n                transition: {\n                    duration: 0.2\n                },\n                style: {\n                    maxWidth: \"80%\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    sx: {\n                        p: 2.5,\n                        borderRadius: 3,\n                        backdropFilter: \"blur(20px)\",\n                        background: isUser ? \"linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08))\" : \"linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.03))\",\n                        border: `1px solid ${isUser ? \"rgba(255, 255, 255, 0.2)\" : \"rgba(255, 255, 255, 0.1)\"}`,\n                        position: \"relative\",\n                        transition: \"all 0.3s ease\",\n                        \"&:hover\": {\n                            background: isUser ? \"linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.12))\" : \"linear-gradient(135deg, rgba(255, 255, 255, 0.12), rgba(255, 255, 255, 0.06))\",\n                            borderColor: isUser ? \"rgba(255, 255, 255, 0.3)\" : \"rgba(255, 255, 255, 0.2)\",\n                            transform: \"translateY(-1px)\",\n                            boxShadow: \"0 8px 32px rgba(0, 0, 0, 0.1)\",\n                            \"& .copy-button\": {\n                                opacity: 1\n                            }\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            title: copied ? \"Copied!\" : \"Copy message\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"copy-button\",\n                                size: \"small\",\n                                onClick: handleCopyMessage,\n                                sx: {\n                                    position: \"absolute\",\n                                    top: 8,\n                                    right: 8,\n                                    opacity: 0,\n                                    transition: \"all 0.3s ease\",\n                                    color: copied ? \"#4caf50\" : \"rgba(255, 255, 255, 0.7)\",\n                                    background: copied ? \"rgba(76, 175, 80, 0.1)\" : \"transparent\",\n                                    \"&:hover\": {\n                                        background: copied ? \"rgba(76, 175, 80, 0.2)\" : \"rgba(255, 255, 255, 0.1)\",\n                                        transform: \"scale(1.1)\"\n                                    }\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                    initial: {\n                                        scale: 0.8,\n                                        opacity: 0\n                                    },\n                                    animate: {\n                                        scale: 1,\n                                        opacity: 1\n                                    },\n                                    transition: {\n                                        duration: 0.2\n                                    },\n                                    children: copied ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Build_Check_ContentCopy_Description_Image_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        fontSize: \"small\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 29\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Build_Check_ContentCopy_Description_Image_mui_icons_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        fontSize: \"small\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 62\n                                    }, this)\n                                }, copied ? \"check\" : \"copy\", false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 13\n                        }, this),\n                        renderAttachments(),\n                        renderToolCall(),\n                        renderToolResult(),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            variant: \"body1\",\n                            sx: {\n                                whiteSpace: \"pre-wrap\",\n                                wordBreak: \"break-word\",\n                                pr: 5,\n                                color: \"white\"\n                            },\n                            children: message.content\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            variant: \"caption\",\n                            sx: {\n                                display: \"block\",\n                                mt: 1,\n                                color: \"rgba(255, 255, 255, 0.5)\",\n                                textAlign: isUser ? \"right\" : \"left\",\n                                fontSize: \"0.75rem\"\n                            },\n                            children: formatTimestamp(message.timestamp)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                lineNumber: 138,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n            lineNumber: 133,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n        lineNumber: 128,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ChatWindow/MessageBubble.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/ModelSelector.tsx":
/*!******************************************!*\
  !*** ./app/components/ModelSelector.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ModelSelector)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Card,CircularProgress,FormControl,InputLabel,MenuItem,Select,Tooltip,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Card,CircularProgress,FormControl,InputLabel,MenuItem,Select,Tooltip,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Card,CircularProgress,FormControl,InputLabel,MenuItem,Select,Tooltip,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Card,CircularProgress,FormControl,InputLabel,MenuItem,Select,Tooltip,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Card,CircularProgress,FormControl,InputLabel,MenuItem,Select,Tooltip,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Card,CircularProgress,FormControl,InputLabel,MenuItem,Select,Tooltip,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/FormControl/FormControl.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Card,CircularProgress,FormControl,InputLabel,MenuItem,Select,Tooltip,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/InputLabel/InputLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Card,CircularProgress,FormControl,InputLabel,MenuItem,Select,Tooltip,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Card,CircularProgress,FormControl,InputLabel,MenuItem,Select,Tooltip,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Card,CircularProgress,FormControl,InputLabel,MenuItem,Select,Tooltip,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Tooltip/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Build_Code_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Build,Code,Visibility!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Build.js\");\n/* harmony import */ var _barrel_optimize_names_Build_Code_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Build,Code,Visibility!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Visibility.js\");\n/* harmony import */ var _barrel_optimize_names_Build_Code_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Build,Code,Visibility!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Code.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction ModelSelector({ selectedModel, onModelSelect, onModelCapabilitiesChange }) {\n    const [models, setModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showDetails, setShowDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchModels();\n    }, []);\n    const fetchModels = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const response = await fetch(\"/api/ollama\");\n            if (!response.ok) {\n                throw new Error(\"Failed to fetch models\");\n            }\n            const data = await response.json();\n            setModels(data.models || []);\n            // Auto-select first model if none selected\n            if (!selectedModel && data.models && data.models.length > 0) {\n                const firstModel = data.models[0];\n                onModelSelect(firstModel.name);\n                onModelCapabilitiesChange?.(firstModel.capabilities);\n            }\n        } catch (err) {\n            console.error(\"Error fetching models:\", err);\n            setError(\"Failed to load Ollama models. Make sure Ollama is running.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const formatModelSize = (size)=>{\n        const gb = size / (1024 * 1024 * 1024);\n        return `${gb.toFixed(1)}GB`;\n    };\n    const getModelFamily = (model)=>{\n        if (model.details?.family) {\n            return model.details.family;\n        }\n        return model.name.split(\":\")[0];\n    };\n    const getCapabilityIcon = (capability)=>{\n        switch(capability){\n            case \"tools\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Build_Code_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    fontSize: \"small\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 28\n                }, this);\n            case \"vision\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Build_Code_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    fontSize: \"small\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 29\n                }, this);\n            case \"code\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Build_Code_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    fontSize: \"small\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 27\n                }, this);\n            default:\n                return null;\n        }\n    };\n    const getCapabilityColor = (capability)=>{\n        switch(capability){\n            case \"tools\":\n                return \"primary\";\n            case \"vision\":\n                return \"secondary\";\n            case \"code\":\n                return \"success\";\n            default:\n                return \"default\";\n        }\n    };\n    const handleModelChange = (modelName)=>{\n        const model = models.find((m)=>m.name === modelName);\n        onModelSelect(modelName);\n        if (model) {\n            onModelCapabilitiesChange?.(model.capabilities);\n        }\n    };\n    const selectedModelData = models.find((m)=>m.name === selectedModel);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n            initial: {\n                opacity: 0,\n                y: 20\n            },\n            animate: {\n                opacity: 1,\n                y: 0\n            },\n            transition: {\n                duration: 0.5\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                sx: {\n                    p: 3,\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        size: 40,\n                        sx: {\n                            mb: 2\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        variant: \"h6\",\n                        gutterBottom: true,\n                        children: \"Discovering Models\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        variant: \"body2\",\n                        color: \"text.secondary\",\n                        children: \"Connecting to Ollama and loading available models...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                lineNumber: 126,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n            lineNumber: 121,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n            initial: {\n                opacity: 0,\n                scale: 0.9\n            },\n            animate: {\n                opacity: 1,\n                scale: 1\n            },\n            transition: {\n                duration: 0.5\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                severity: \"error\",\n                sx: {\n                    borderRadius: 3\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        variant: \"h6\",\n                        gutterBottom: true,\n                        children: \"Connection Error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 11\n                    }, this),\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                lineNumber: 146,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n            lineNumber: 141,\n            columnNumber: 7\n        }, this);\n    }\n    if (models.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n            initial: {\n                opacity: 0,\n                scale: 0.9\n            },\n            animate: {\n                opacity: 1,\n                scale: 1\n            },\n            transition: {\n                duration: 0.5\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                severity: \"warning\",\n                sx: {\n                    borderRadius: 3\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        variant: \"h6\",\n                        gutterBottom: true,\n                        children: \"No Models Found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 11\n                    }, this),\n                    \"No Ollama models found. Install a model using: \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        children: \"ollama pull llama3.2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 58\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                lineNumber: 163,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n            lineNumber: 158,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            fullWidth: true,\n            size: \"small\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    sx: {\n                        color: \"rgba(255, 255, 255, 0.7)\"\n                    },\n                    children: \"Model\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    value: selectedModel || \"\",\n                    label: \"Model\",\n                    onChange: (e)=>handleModelChange(e.target.value),\n                    sx: {\n                        \"& .MuiOutlinedInput-notchedOutline\": {\n                            borderColor: \"rgba(255, 255, 255, 0.2)\"\n                        },\n                        \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                            borderColor: \"rgba(255, 255, 255, 0.4)\"\n                        },\n                        \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                            borderColor: \"rgba(255, 255, 255, 0.6)\"\n                        },\n                        \"& .MuiSelect-select\": {\n                            color: \"white\"\n                        },\n                        \"& .MuiSvgIcon-root\": {\n                            color: \"rgba(255, 255, 255, 0.7)\"\n                        }\n                    },\n                    MenuProps: {\n                        PaperProps: {\n                            sx: {\n                                backdropFilter: \"blur(20px)\",\n                                background: \"rgba(255, 255, 255, 0.1)\",\n                                border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                                \"& .MuiMenuItem-root\": {\n                                    color: \"white\",\n                                    \"&:hover\": {\n                                        backgroundColor: \"rgba(255, 255, 255, 0.1)\"\n                                    },\n                                    \"&.Mui-selected\": {\n                                        backgroundColor: \"rgba(255, 255, 255, 0.2)\",\n                                        \"&:hover\": {\n                                            backgroundColor: \"rgba(255, 255, 255, 0.25)\"\n                                        }\n                                    }\n                                }\n                            }\n                        }\n                    },\n                    renderValue: (value)=>{\n                        const model = models.find((m)=>m.name === value);\n                        if (!model) return value;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            gap: 1,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    variant: \"body2\",\n                                    sx: {\n                                        flex: 1\n                                    },\n                                    children: model.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 17\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    display: \"flex\",\n                                    gap: 0.5,\n                                    children: [\n                                        model.capabilities.supportsTools && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            sx: {\n                                                width: 6,\n                                                height: 6,\n                                                borderRadius: \"50%\",\n                                                bgcolor: \"white\",\n                                                opacity: 0.7\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        model.capabilities.supportsVision && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            sx: {\n                                                width: 6,\n                                                height: 6,\n                                                borderRadius: \"50%\",\n                                                bgcolor: \"white\",\n                                                opacity: 0.5\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        model.capabilities.supportsCode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            sx: {\n                                                width: 6,\n                                                height: 6,\n                                                borderRadius: \"50%\",\n                                                bgcolor: \"white\",\n                                                opacity: 0.3\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 17\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 15\n                        }, void 0);\n                    },\n                    children: models.map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            value: model.name,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                justifyContent: \"space-between\",\n                                width: \"100%\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                variant: \"body2\",\n                                                fontWeight: 500,\n                                                children: model.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                variant: \"caption\",\n                                                color: \"text.secondary\",\n                                                children: [\n                                                    formatModelSize(model.size),\n                                                    \" • \",\n                                                    getModelFamily(model)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        display: \"flex\",\n                                        gap: 0.5,\n                                        children: [\n                                            model.capabilities.supportsTools && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                title: \"Tools\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    sx: {\n                                                        width: 8,\n                                                        height: 8,\n                                                        borderRadius: \"50%\",\n                                                        bgcolor: \"white\",\n                                                        opacity: 0.8\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 23\n                                            }, this),\n                                            model.capabilities.supportsVision && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                title: \"Vision\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    sx: {\n                                                        width: 8,\n                                                        height: 8,\n                                                        borderRadius: \"50%\",\n                                                        bgcolor: \"white\",\n                                                        opacity: 0.6\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 23\n                                            }, this),\n                                            model.capabilities.supportsCode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                title: \"Code\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    sx: {\n                                                        width: 8,\n                                                        height: 8,\n                                                        borderRadius: \"50%\",\n                                                        bgcolor: \"white\",\n                                                        opacity: 0.4\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 17\n                            }, this)\n                        }, model.name, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 15\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n            lineNumber: 175,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n        lineNumber: 174,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ModelSelector.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/ServerConfigDialog.tsx":
/*!***********************************************!*\
  !*** ./app/components/ServerConfigDialog.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ServerConfigDialog)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Accordion/Accordion.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/AccordionSummary/AccordionSummary.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/AccordionDetails/AccordionDetails.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Close_Delete_ExpandMore_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Close,Delete,ExpandMore!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Close.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Close_Delete_ExpandMore_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Close,Delete,ExpandMore!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Add.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Close_Delete_ExpandMore_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Close,Delete,ExpandMore!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/ExpandMore.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Close_Delete_ExpandMore_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Close,Delete,ExpandMore!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Delete.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction ServerConfigDialog({ open, onClose, onConfigUpdate }) {\n    const [config, setConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        mcpServers: {}\n    });\n    const [configText, setConfigText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (open) {\n            loadConfig();\n        }\n    }, [\n        open\n    ]);\n    const loadConfig = async ()=>{\n        try {\n            const response = await fetch(\"/api/mcp\");\n            if (response.ok) {\n                const data = await response.json();\n                setConfig({\n                    mcpServers: data.mcpServers || {}\n                });\n                setConfigText(JSON.stringify({\n                    mcpServers: data.mcpServers || {}\n                }, null, 2));\n            }\n        } catch (err) {\n            console.error(\"Error loading config:\", err);\n            setError(\"Failed to load configuration\");\n        }\n    };\n    const handleConfigTextChange = (value)=>{\n        setConfigText(value);\n        setError(null);\n        try {\n            const parsed = JSON.parse(value);\n            if (parsed.mcpServers && typeof parsed.mcpServers === \"object\") {\n                setConfig(parsed);\n            } else {\n                setError('Configuration must have a \"mcpServers\" object');\n            }\n        } catch (err) {\n            setError(\"Invalid JSON syntax\");\n        }\n    };\n    const handleSave = async ()=>{\n        if (error) {\n            return;\n        }\n        try {\n            setLoading(true);\n            const response = await fetch(\"/api/mcp\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    action: \"update_config\",\n                    config\n                })\n            });\n            if (response.ok) {\n                onConfigUpdate(config);\n                onClose();\n            } else {\n                setError(\"Failed to save configuration\");\n            }\n        } catch (err) {\n            console.error(\"Error saving config:\", err);\n            setError(\"Failed to save configuration\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const addNewServer = ()=>{\n        const newServerName = `server_${Date.now()}`;\n        const newConfig = {\n            ...config,\n            mcpServers: {\n                ...config.mcpServers,\n                [newServerName]: {\n                    command: \"npx\",\n                    args: [\n                        \"@smithery/cli@latest\",\n                        \"run\",\n                        \"example\"\n                    ],\n                    description: \"New MCP server\"\n                }\n            }\n        };\n        setConfig(newConfig);\n        setConfigText(JSON.stringify(newConfig, null, 2));\n    };\n    const removeServer = (serverName)=>{\n        const newConfig = {\n            ...config\n        };\n        delete newConfig.mcpServers[serverName];\n        setConfig(newConfig);\n        setConfigText(JSON.stringify(newConfig, null, 2));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        open: open,\n        onClose: onClose,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            variant: \"h6\",\n                            children: \"MCP Server Configuration\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            onClick: onClose,\n                            size: \"small\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Close_Delete_ExpandMore_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        mb: 2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            variant: \"body2\",\n                            color: \"text.secondary\",\n                            gutterBottom: true,\n                            children: \"Configure Model Context Protocol (MCP) servers to extend the chat capabilities with external tools.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        severity: \"error\",\n                        sx: {\n                            mb: 2\n                        },\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        mb: 3,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                display: \"flex\",\n                                justifyContent: \"space-between\",\n                                alignItems: \"center\",\n                                mb: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        variant: \"h6\",\n                                        children: \"Configured Servers\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Close_Delete_ExpandMore_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 26\n                                        }, void 0),\n                                        onClick: addNewServer,\n                                        variant: \"outlined\",\n                                        size: \"small\",\n                                        children: \"Add Server\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 11\n                            }, this),\n                            Object.entries(config.mcpServers).map(([name, serverConfig])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    sx: {\n                                        mb: 1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            expandIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Close_Delete_ExpandMore_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 45\n                                            }, void 0),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                gap: 2,\n                                                width: \"100%\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        variant: \"subtitle2\",\n                                                        children: name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        label: serverConfig.command,\n                                                        size: \"small\",\n                                                        variant: \"outlined\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    serverConfig.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        variant: \"caption\",\n                                                        color: \"text.secondary\",\n                                                        children: serverConfig.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        flexGrow: 1\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        size: \"small\",\n                                                        onClick: (e)=>{\n                                                            e.stopPropagation();\n                                                            removeServer(name);\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Close_Delete_ExpandMore_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            fontSize: \"small\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                                            lineNumber: 197,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        gutterBottom: true,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Command:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                                                lineNumber: 204,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" \",\n                                                            serverConfig.command\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        gutterBottom: true,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Arguments:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                                                lineNumber: 207,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" \",\n                                                            serverConfig.args.join(\" \")\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    serverConfig.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        gutterBottom: true,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Description:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                                                lineNumber: 211,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" \",\n                                                            serverConfig.description\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, name, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                variant: \"h6\",\n                                gutterBottom: true,\n                                children: \"Raw Configuration (JSON)\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                multiline: true,\n                                rows: 12,\n                                fullWidth: true,\n                                value: configText,\n                                onChange: (e)=>handleConfigTextChange(e.target.value),\n                                variant: \"outlined\",\n                                placeholder: \"Enter MCP configuration JSON...\",\n                                sx: {\n                                    \"& .MuiInputBase-input\": {\n                                        fontFamily: \"monospace\",\n                                        fontSize: \"0.875rem\"\n                                    }\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        onClick: onClose,\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        onClick: handleSave,\n                        variant: \"contained\",\n                        disabled: !!error || loading,\n                        children: loading ? \"Saving...\" : \"Save Configuration\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                lineNumber: 243,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n        lineNumber: 137,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ServerConfigDialog.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/ThemeProvider.tsx":
/*!******************************************!*\
  !*** ./app/components/ThemeProvider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/material/styles */ \"(ssr)/./node_modules/@mui/material/styles/ThemeProvider.js\");\n/* harmony import */ var _mui_material_CssBaseline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mui/material/CssBaseline */ \"(ssr)/./node_modules/@mui/material/CssBaseline/CssBaseline.js\");\n/* harmony import */ var _theme_theme__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../theme/theme */ \"(ssr)/./app/theme/theme.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction ThemeProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_styles__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        theme: _theme_theme__WEBPACK_IMPORTED_MODULE_2__.theme,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_CssBaseline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ThemeProvider.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ThemeProvider.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy9UaGVtZVByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFFMEI7QUFDK0M7QUFDckI7QUFDYjtBQU14QixTQUFTQyxjQUFjLEVBQUVJLFFBQVEsRUFBc0I7SUFDcEUscUJBQ0UsOERBQUNILDREQUFnQkE7UUFBQ0UsT0FBT0EsK0NBQUtBOzswQkFDNUIsOERBQUNELGlFQUFXQTs7Ozs7WUFDWEU7Ozs7Ozs7QUFHUCIsInNvdXJjZXMiOlsid2VicGFjazovL29sbGFtYS1tY3AtY2hhdC8uL2FwcC9jb21wb25lbnRzL1RoZW1lUHJvdmlkZXIudHN4P2MyY2MiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgVGhlbWVQcm92aWRlciBhcyBNdWlUaGVtZVByb3ZpZGVyIH0gZnJvbSAnQG11aS9tYXRlcmlhbC9zdHlsZXMnO1xuaW1wb3J0IENzc0Jhc2VsaW5lIGZyb20gJ0BtdWkvbWF0ZXJpYWwvQ3NzQmFzZWxpbmUnO1xuaW1wb3J0IHsgdGhlbWUgfSBmcm9tICcuLi90aGVtZS90aGVtZSc7XG5cbmludGVyZmFjZSBUaGVtZVByb3ZpZGVyUHJvcHMge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBUaGVtZVByb3ZpZGVyKHsgY2hpbGRyZW4gfTogVGhlbWVQcm92aWRlclByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPE11aVRoZW1lUHJvdmlkZXIgdGhlbWU9e3RoZW1lfT5cbiAgICAgIDxDc3NCYXNlbGluZSAvPlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvTXVpVGhlbWVQcm92aWRlcj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlRoZW1lUHJvdmlkZXIiLCJNdWlUaGVtZVByb3ZpZGVyIiwiQ3NzQmFzZWxpbmUiLCJ0aGVtZSIsImNoaWxkcmVuIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ThemeProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/TypingIndicator.tsx":
/*!********************************************!*\
  !*** ./app/components/TypingIndicator.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TypingIndicator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction TypingIndicator() {\n    const dotVariants = {\n        initial: {\n            y: 0\n        },\n        animate: {\n            y: -10\n        }\n    };\n    const containerVariants = {\n        initial: {\n            opacity: 0\n        },\n        animate: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.2,\n                repeat: Infinity,\n                repeatType: \"reverse\"\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        display: \"flex\",\n        justifyContent: \"flex-start\",\n        mb: 3,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            sx: {\n                p: 2.5,\n                borderRadius: 3,\n                backdropFilter: \"blur(20px)\",\n                background: \"linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.03))\",\n                border: \"1px solid rgba(255, 255, 255, 0.1)\",\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: 2,\n                minWidth: 120\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    variants: containerVariants,\n                    initial: \"initial\",\n                    animate: \"animate\",\n                    style: {\n                        display: \"flex\",\n                        gap: 4,\n                        alignItems: \"center\"\n                    },\n                    children: [\n                        0,\n                        1,\n                        2\n                    ].map((index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            variants: dotVariants,\n                            transition: {\n                                duration: 0.6,\n                                ease: \"easeInOut\",\n                                delay: index * 0.2\n                            },\n                            style: {\n                                width: 8,\n                                height: 8,\n                                borderRadius: \"50%\",\n                                backgroundColor: \"rgba(255, 255, 255, 0.7)\"\n                            }\n                        }, index, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\TypingIndicator.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\TypingIndicator.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    variant: \"body2\",\n                    sx: {\n                        color: \"rgba(255, 255, 255, 0.7)\",\n                        fontStyle: \"italic\"\n                    },\n                    children: \"AI is thinking...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\TypingIndicator.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\TypingIndicator.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\TypingIndicator.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/TypingIndicator.tsx\n");

/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Tooltip,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Tooltip,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(ssr)/./node_modules/@mui/system/esm/useMediaQuery/useMediaQuery.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Tooltip,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Tooltip,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Tooltip,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Tooltip,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Tooltip/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Tooltip,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Tooltip,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Tooltip,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Tooltip,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Tooltip,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _barrel_optimize_names_Delete_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Delete,Warning!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Delete.js\");\n/* harmony import */ var _barrel_optimize_names_Delete_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Delete,Warning!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Warning.js\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! uuid */ \"(ssr)/./node_modules/uuid/dist/esm-node/v4.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _components_ModelSelector__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/ModelSelector */ \"(ssr)/./app/components/ModelSelector.tsx\");\n/* harmony import */ var _components_ServerConfigDialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/ServerConfigDialog */ \"(ssr)/./app/components/ServerConfigDialog.tsx\");\n/* harmony import */ var _components_ChatWindow_ChatWindow__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/ChatWindow/ChatWindow */ \"(ssr)/./app/components/ChatWindow/ChatWindow.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction HomePage() {\n    const theme = (0,_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    const isMobile = (0,_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(theme.breakpoints.down(\"md\"));\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [configDialogOpen, setConfigDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!isMobile);\n    const [mcpConfig, setMcpConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        mcpServers: {}\n    });\n    const [availableTools, setAvailableTools] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [conversations, setConversations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [activeConversationId, setActiveConversationId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [modelCapabilities, setModelCapabilities] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deleteDialogOpen, setDeleteDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadMCPConfig();\n        loadConversations();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setSidebarOpen(!isMobile);\n    }, [\n        isMobile\n    ]);\n    const loadMCPConfig = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch(\"/api/mcp\");\n            if (response.ok) {\n                const data = await response.json();\n                console.log(\"Loaded MCP data:\", data);\n                console.log(\"Available tools:\", data.tools);\n                setMcpConfig({\n                    mcpServers: data.mcpServers || {}\n                });\n                setAvailableTools(data.tools || []);\n                console.log(\"Set available tools state:\", data.tools || []);\n            }\n        } catch (error) {\n            console.error(\"Error loading MCP config:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadConversations = ()=>{\n        // Load conversations from localStorage\n        const saved = localStorage.getItem(\"ollama-chat-conversations\");\n        if (saved) {\n            try {\n                const parsed = JSON.parse(saved);\n                setConversations(parsed.map((conv)=>({\n                        ...conv,\n                        timestamp: new Date(conv.timestamp),\n                        messages: conv.messages.map((msg)=>({\n                                ...msg,\n                                timestamp: new Date(msg.timestamp)\n                            }))\n                    })));\n            } catch (error) {\n                console.error(\"Error loading conversations:\", error);\n            }\n        }\n    };\n    const saveConversations = (convs)=>{\n        localStorage.setItem(\"ollama-chat-conversations\", JSON.stringify(convs));\n    };\n    const handleConfigUpdate = (newConfig)=>{\n        setMcpConfig(newConfig);\n        loadMCPConfig(); // Reload to get updated tools\n    };\n    const handleRefresh = ()=>{\n        loadMCPConfig();\n    };\n    const createNewConversation = ()=>{\n        const newConversation = {\n            id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n            title: \"New Conversation\",\n            lastMessage: \"\",\n            timestamp: new Date(),\n            messageCount: 0,\n            messages: []\n        };\n        const updatedConversations = [\n            newConversation,\n            ...conversations\n        ];\n        setConversations(updatedConversations);\n        setActiveConversationId(newConversation.id);\n        saveConversations(updatedConversations);\n        if (isMobile) {\n            setSidebarOpen(false);\n        }\n    };\n    const selectConversation = (id)=>{\n        setActiveConversationId(id);\n        if (isMobile) {\n            setSidebarOpen(false);\n        }\n    };\n    const deleteConversation = (id)=>{\n        const updatedConversations = conversations.filter((conv)=>conv.id !== id);\n        setConversations(updatedConversations);\n        saveConversations(updatedConversations);\n        if (activeConversationId === id) {\n            setActiveConversationId(updatedConversations.length > 0 ? updatedConversations[0].id : null);\n        }\n    };\n    const handleDeleteCurrentChat = ()=>{\n        if (activeConversationId) {\n            deleteConversation(activeConversationId);\n            setDeleteDialogOpen(false);\n        }\n    };\n    const renameConversation = (id, newTitle)=>{\n        const updatedConversations = conversations.map((conv)=>conv.id === id ? {\n                ...conv,\n                title: newTitle\n            } : conv);\n        setConversations(updatedConversations);\n        saveConversations(updatedConversations);\n    };\n    const updateConversationWithMessage = (message)=>{\n        const activeConv = conversations.find((conv)=>conv.id === activeConversationId);\n        if (!activeConv) return;\n        const updatedConversation = {\n            ...activeConv,\n            messages: [\n                ...activeConv.messages,\n                message\n            ],\n            lastMessage: message.content,\n            timestamp: new Date(),\n            messageCount: activeConv.messageCount + 1,\n            title: activeConv.title === \"New Conversation\" && message.role === \"user\" ? message.content.substring(0, 50) + (message.content.length > 50 ? \"...\" : \"\") : activeConv.title\n        };\n        const updatedConversations = conversations.map((conv)=>conv.id === activeConversationId ? updatedConversation : conv);\n        setConversations(updatedConversations);\n        saveConversations(updatedConversations);\n    };\n    const getActiveConversation = ()=>{\n        return conversations.find((conv)=>conv.id === activeConversationId) || null;\n    };\n    const serverCount = Object.keys(mcpConfig.mcpServers).length;\n    const isConnected = selectedModel !== null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n        initial: {\n            opacity: 0\n        },\n        animate: {\n            opacity: 1\n        },\n        transition: {\n            duration: 0.5\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            sx: {\n                height: \"100vh\",\n                display: \"flex\",\n                overflow: \"hidden\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    sx: {\n                        width: sidebarOpen ? 320 : 0,\n                        transition: \"width 0.3s ease\",\n                        overflow: \"hidden\",\n                        borderRight: sidebarOpen ? \"1px solid rgba(255, 255, 255, 0.1)\" : \"none\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        sx: {\n                            width: 320,\n                            height: \"100%\",\n                            display: \"flex\",\n                            flexDirection: \"column\",\n                            backdropFilter: \"blur(20px)\",\n                            background: \"rgba(255, 255, 255, 0.03)\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                sx: {\n                                    p: 3,\n                                    borderBottom: \"1px solid rgba(255, 255, 255, 0.1)\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        variant: \"h6\",\n                                        sx: {\n                                            mb: 2,\n                                            fontWeight: 600\n                                        },\n                                        children: \"Ollama Chat\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ModelSelector__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        selectedModel: selectedModel,\n                                        onModelSelect: setSelectedModel,\n                                        onModelCapabilitiesChange: setModelCapabilities\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                sx: {\n                                    flex: 1,\n                                    overflow: \"auto\",\n                                    p: 2\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        sx: {\n                                            mb: 2\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            fullWidth: true,\n                                            variant: \"outlined\",\n                                            onClick: createNewConversation,\n                                            sx: {\n                                                mb: 2\n                                            },\n                                            children: \"New Chat\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 13\n                                    }, this),\n                                    conversations.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        variant: \"body2\",\n                                        color: \"text.secondary\",\n                                        sx: {\n                                            textAlign: \"center\",\n                                            py: 4\n                                        },\n                                        children: \"No conversations yet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 15\n                                    }, this) : conversations.map((conversation)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            onClick: ()=>selectConversation(conversation.id),\n                                            sx: {\n                                                p: 2,\n                                                mb: 1,\n                                                borderRadius: 2,\n                                                cursor: \"pointer\",\n                                                background: conversation.id === activeConversationId ? \"rgba(255, 255, 255, 0.1)\" : \"transparent\",\n                                                border: conversation.id === activeConversationId ? \"1px solid rgba(255, 255, 255, 0.2)\" : \"1px solid transparent\",\n                                                transition: \"all 0.2s ease\",\n                                                \"&:hover\": {\n                                                    background: \"rgba(255, 255, 255, 0.05)\"\n                                                }\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    variant: \"body2\",\n                                                    fontWeight: 500,\n                                                    noWrap: true,\n                                                    children: conversation.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    variant: \"caption\",\n                                                    color: \"text.secondary\",\n                                                    noWrap: true,\n                                                    children: [\n                                                        conversation.messageCount,\n                                                        \" messages\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, conversation.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 17\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                sx: {\n                                    p: 2,\n                                    borderTop: \"1px solid rgba(255, 255, 255, 0.1)\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    fullWidth: true,\n                                    variant: \"outlined\",\n                                    onClick: ()=>setConfigDialogOpen(true),\n                                    size: \"small\",\n                                    children: \"Settings\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                    lineNumber: 207,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    sx: {\n                        flex: 1,\n                        display: \"flex\",\n                        flexDirection: \"column\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            sx: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                p: 2,\n                                borderBottom: \"1px solid rgba(255, 255, 255, 0.1)\",\n                                backdropFilter: \"blur(20px)\",\n                                background: \"rgba(255, 255, 255, 0.03)\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    onClick: ()=>setSidebarOpen(!sidebarOpen),\n                                    sx: {\n                                        mr: 2,\n                                        minWidth: \"auto\",\n                                        p: 1\n                                    },\n                                    children: \"☰\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    variant: \"h6\",\n                                    sx: {\n                                        flex: 1\n                                    },\n                                    children: getActiveConversation()?.title || \"Select a conversation\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 11\n                                }, this),\n                                activeConversationId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    title: \"Delete current chat\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        onClick: ()=>setDeleteDialogOpen(true),\n                                        sx: {\n                                            color: \"rgba(255, 255, 255, 0.7)\",\n                                            \"&:hover\": {\n                                                color: \"#ff4444\",\n                                                backgroundColor: \"rgba(255, 68, 68, 0.1)\"\n                                            },\n                                            mr: 1\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Delete_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 13\n                                }, this),\n                                selectedModel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    sx: {\n                                        display: \"flex\",\n                                        gap: 1\n                                    },\n                                    children: [\n                                        modelCapabilities?.supportsTools && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            sx: {\n                                                px: 1,\n                                                py: 0.5,\n                                                borderRadius: 1,\n                                                background: \"rgba(255, 255, 255, 0.1)\",\n                                                fontSize: \"0.75rem\"\n                                            },\n                                            children: \"Tools\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 17\n                                        }, this),\n                                        modelCapabilities?.supportsVision && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            sx: {\n                                                px: 1,\n                                                py: 0.5,\n                                                borderRadius: 1,\n                                                background: \"rgba(255, 255, 255, 0.1)\",\n                                                fontSize: \"0.75rem\"\n                                            },\n                                            children: \"Vision\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            sx: {\n                                flex: 1,\n                                overflow: \"hidden\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatWindow_ChatWindow__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                selectedModel: selectedModel,\n                                availableTools: availableTools,\n                                conversation: getActiveConversation(),\n                                onMessageSent: updateConversationWithMessage,\n                                modelCapabilities: modelCapabilities\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                lineNumber: 376,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                            lineNumber: 375,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                    lineNumber: 304,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ServerConfigDialog__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    open: configDialogOpen,\n                    onClose: ()=>setConfigDialogOpen(false),\n                    onConfigUpdate: handleConfigUpdate\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                    lineNumber: 387,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    open: deleteDialogOpen,\n                    onClose: ()=>setDeleteDialogOpen(false),\n                    PaperProps: {\n                        sx: {\n                            backdropFilter: \"blur(20px)\",\n                            background: \"rgba(255, 255, 255, 0.1)\",\n                            border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                            color: \"white\"\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            sx: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                color: \"white\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Delete_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    sx: {\n                                        mr: 1,\n                                        color: \"#ff4444\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 11\n                                }, this),\n                                \"Delete Chat\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                            lineNumber: 406,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                color: \"rgba(255, 255, 255, 0.8)\",\n                                children: [\n                                    'Are you sure you want to delete \"',\n                                    getActiveConversation()?.title,\n                                    '\"? This action cannot be undone and all messages in this conversation will be permanently lost.'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                lineNumber: 411,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                            lineNumber: 410,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    onClick: ()=>setDeleteDialogOpen(false),\n                                    sx: {\n                                        color: \"rgba(255, 255, 255, 0.7)\"\n                                    },\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                    lineNumber: 417,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    onClick: handleDeleteCurrentChat,\n                                    variant: \"contained\",\n                                    sx: {\n                                        bgcolor: \"#ff4444\",\n                                        \"&:hover\": {\n                                            bgcolor: \"#ff3333\"\n                                        }\n                                    },\n                                    children: \"Delete\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                            lineNumber: 416,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                    lineNumber: 394,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n            lineNumber: 205,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n        lineNumber: 200,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./app/theme/theme.ts":
/*!****************************!*\
  !*** ./app/theme/theme.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   theme: () => (/* binding */ theme)\n/* harmony export */ });\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @mui/material/styles */ \"(ssr)/./node_modules/@mui/material/styles/createTheme.js\");\n/* __next_internal_client_entry_do_not_use__ theme auto */ \n// Minimalist black and white glassmorphism palette\nconst palette = {\n    primary: {\n        main: \"#ffffff\",\n        light: \"#ffffff\",\n        dark: \"#f5f5f5\",\n        contrastText: \"#000000\",\n        50: \"#ffffff\",\n        100: \"#f5f5f5\",\n        200: \"#eeeeee\",\n        300: \"#e0e0e0\",\n        400: \"#bdbdbd\",\n        500: \"#9e9e9e\",\n        600: \"#757575\",\n        700: \"#616161\",\n        800: \"#424242\",\n        900: \"#212121\"\n    },\n    secondary: {\n        main: \"#000000\",\n        light: \"#424242\",\n        dark: \"#000000\",\n        contrastText: \"#ffffff\",\n        50: \"#fafafa\",\n        100: \"#f5f5f5\",\n        200: \"#eeeeee\",\n        300: \"#e0e0e0\",\n        400: \"#bdbdbd\",\n        500: \"#9e9e9e\",\n        600: \"#757575\",\n        700: \"#616161\",\n        800: \"#424242\",\n        900: \"#212121\"\n    },\n    success: {\n        main: \"#ffffff\",\n        light: \"#ffffff\",\n        dark: \"#f5f5f5\",\n        50: \"#ffffff\",\n        100: \"#f5f5f5\",\n        200: \"#eeeeee\",\n        300: \"#e0e0e0\",\n        400: \"#bdbdbd\",\n        500: \"#9e9e9e\",\n        600: \"#757575\",\n        700: \"#616161\",\n        800: \"#424242\",\n        900: \"#212121\"\n    },\n    warning: {\n        main: \"#9e9e9e\",\n        light: \"#bdbdbd\",\n        dark: \"#757575\",\n        50: \"#fafafa\",\n        100: \"#f5f5f5\",\n        200: \"#eeeeee\",\n        300: \"#e0e0e0\",\n        400: \"#bdbdbd\",\n        500: \"#9e9e9e\",\n        600: \"#757575\",\n        700: \"#616161\",\n        800: \"#424242\",\n        900: \"#212121\"\n    },\n    error: {\n        main: \"#757575\",\n        light: \"#9e9e9e\",\n        dark: \"#424242\",\n        50: \"#fafafa\",\n        100: \"#f5f5f5\",\n        200: \"#eeeeee\",\n        300: \"#e0e0e0\",\n        400: \"#bdbdbd\",\n        500: \"#9e9e9e\",\n        600: \"#757575\",\n        700: \"#616161\",\n        800: \"#424242\",\n        900: \"#212121\"\n    },\n    background: {\n        default: \"linear-gradient(135deg, #000000 0%, #1a1a1a 50%, #000000 100%)\",\n        paper: \"rgba(255, 255, 255, 0.05)\",\n        glass: \"rgba(255, 255, 255, 0.03)\",\n        glassDark: \"rgba(0, 0, 0, 0.2)\"\n    },\n    text: {\n        primary: \"#ffffff\",\n        secondary: \"rgba(255, 255, 255, 0.7)\",\n        disabled: \"rgba(255, 255, 255, 0.3)\"\n    },\n    divider: \"rgba(255, 255, 255, 0.08)\",\n    action: {\n        hover: \"rgba(255, 255, 255, 0.05)\",\n        selected: \"rgba(255, 255, 255, 0.08)\",\n        disabled: \"rgba(255, 255, 255, 0.2)\",\n        disabledBackground: \"rgba(255, 255, 255, 0.05)\"\n    }\n};\n// Modern typography with better hierarchy\nconst typography = {\n    fontFamily: [\n        '\"Inter\"',\n        \"-apple-system\",\n        \"BlinkMacSystemFont\",\n        '\"Segoe UI\"',\n        \"Roboto\",\n        '\"Helvetica Neue\"',\n        \"Arial\",\n        \"sans-serif\"\n    ].join(\",\"),\n    h1: {\n        fontSize: \"2.5rem\",\n        fontWeight: 800,\n        lineHeight: 1.1,\n        letterSpacing: \"-0.025em\",\n        background: \"linear-gradient(135deg, #ffffff 0%, #e0e7ff 100%)\",\n        WebkitBackgroundClip: \"text\",\n        WebkitTextFillColor: \"transparent\",\n        backgroundClip: \"text\"\n    },\n    h2: {\n        fontSize: \"2rem\",\n        fontWeight: 700,\n        lineHeight: 1.2,\n        letterSpacing: \"-0.025em\"\n    },\n    h3: {\n        fontSize: \"1.75rem\",\n        fontWeight: 600,\n        lineHeight: 1.3,\n        letterSpacing: \"-0.015em\"\n    },\n    h4: {\n        fontSize: \"1.5rem\",\n        fontWeight: 600,\n        lineHeight: 1.3,\n        letterSpacing: \"-0.015em\"\n    },\n    h5: {\n        fontSize: \"1.25rem\",\n        fontWeight: 600,\n        lineHeight: 1.4,\n        letterSpacing: \"-0.01em\"\n    },\n    h6: {\n        fontSize: \"1.125rem\",\n        fontWeight: 600,\n        lineHeight: 1.4,\n        letterSpacing: \"-0.01em\"\n    },\n    body1: {\n        fontSize: \"1rem\",\n        lineHeight: 1.6,\n        fontWeight: 400\n    },\n    body2: {\n        fontSize: \"0.875rem\",\n        lineHeight: 1.5,\n        fontWeight: 400\n    },\n    caption: {\n        fontSize: \"0.75rem\",\n        lineHeight: 1.4,\n        fontWeight: 500,\n        letterSpacing: \"0.025em\",\n        textTransform: \"uppercase\"\n    },\n    button: {\n        fontWeight: 600,\n        letterSpacing: \"0.025em\",\n        textTransform: \"none\"\n    }\n};\n// Custom spacing and breakpoints\nconst spacing = 8;\nconst breakpoints = {\n    values: {\n        xs: 0,\n        sm: 640,\n        md: 768,\n        lg: 1024,\n        xl: 1280\n    }\n};\n// Modern glassmorphism component overrides\nconst components = {\n    MuiCssBaseline: {\n        styleOverrides: {\n            \"*\": {\n                boxSizing: \"border-box\"\n            },\n            html: {\n                height: \"100%\"\n            },\n            body: {\n                height: \"100%\",\n                margin: 0,\n                padding: 0,\n                background: \"linear-gradient(135deg, #000000 0%, #1a1a1a 50%, #000000 100%)\",\n                backgroundAttachment: \"fixed\",\n                scrollbarWidth: \"thin\",\n                scrollbarColor: \"rgba(255, 255, 255, 0.2) transparent\",\n                \"&::-webkit-scrollbar\": {\n                    width: \"6px\"\n                },\n                \"&::-webkit-scrollbar-track\": {\n                    background: \"transparent\"\n                },\n                \"&::-webkit-scrollbar-thumb\": {\n                    background: \"rgba(255, 255, 255, 0.2)\",\n                    borderRadius: \"10px\",\n                    border: \"none\"\n                },\n                \"&::-webkit-scrollbar-thumb:hover\": {\n                    background: \"rgba(255, 255, 255, 0.3)\"\n                }\n            },\n            \"#__next\": {\n                height: \"100%\"\n            }\n        }\n    },\n    MuiButton: {\n        styleOverrides: {\n            root: {\n                textTransform: \"none\",\n                borderRadius: \"16px\",\n                fontWeight: 600,\n                padding: \"12px 24px\",\n                backdropFilter: \"blur(20px)\",\n                background: \"rgba(255, 255, 255, 0.05)\",\n                border: \"1px solid rgba(255, 255, 255, 0.1)\",\n                color: \"white\",\n                transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n                \"&:hover\": {\n                    background: \"rgba(255, 255, 255, 0.1)\",\n                    transform: \"translateY(-1px)\",\n                    boxShadow: \"0 10px 30px rgba(0, 0, 0, 0.3)\"\n                },\n                \"&:active\": {\n                    transform: \"translateY(0px)\"\n                }\n            },\n            contained: {\n                background: \"linear-gradient(135deg, #ffffff 0%, #f5f5f5 100%)\",\n                border: \"none\",\n                color: \"#000000\",\n                boxShadow: \"0 10px 30px rgba(0, 0, 0, 0.2)\",\n                \"&:hover\": {\n                    background: \"linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%)\",\n                    boxShadow: \"0 15px 40px rgba(0, 0, 0, 0.3)\"\n                }\n            },\n            outlined: {\n                borderColor: \"rgba(255, 255, 255, 0.2)\",\n                \"&:hover\": {\n                    borderColor: \"rgba(255, 255, 255, 0.3)\",\n                    background: \"rgba(255, 255, 255, 0.05)\"\n                }\n            }\n        }\n    },\n    MuiPaper: {\n        styleOverrides: {\n            root: {\n                borderRadius: \"24px\",\n                backdropFilter: \"blur(20px)\",\n                background: \"rgba(255, 255, 255, 0.1)\",\n                border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                boxShadow: \"0 20px 40px rgba(0, 0, 0, 0.1)\",\n                transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n                \"&:hover\": {\n                    background: \"rgba(255, 255, 255, 0.15)\",\n                    transform: \"translateY(-2px)\",\n                    boxShadow: \"0 30px 60px rgba(0, 0, 0, 0.15)\"\n                }\n            }\n        }\n    },\n    MuiTextField: {\n        styleOverrides: {\n            root: {\n                \"& .MuiOutlinedInput-root\": {\n                    borderRadius: \"16px\",\n                    backdropFilter: \"blur(20px)\",\n                    background: \"rgba(255, 255, 255, 0.1)\",\n                    border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                    color: \"white\",\n                    transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n                    \"& fieldset\": {\n                        border: \"none\"\n                    },\n                    \"&:hover\": {\n                        background: \"rgba(255, 255, 255, 0.15)\",\n                        transform: \"translateY(-1px)\"\n                    },\n                    \"&.Mui-focused\": {\n                        background: \"rgba(255, 255, 255, 0.2)\",\n                        boxShadow: \"0 0 0 2px rgba(99, 102, 241, 0.5)\"\n                    }\n                },\n                \"& .MuiInputLabel-root\": {\n                    color: \"rgba(255, 255, 255, 0.7)\",\n                    \"&.Mui-focused\": {\n                        color: \"#6366f1\"\n                    }\n                },\n                \"& .MuiOutlinedInput-input\": {\n                    color: \"white\",\n                    \"&::placeholder\": {\n                        color: \"rgba(255, 255, 255, 0.5)\",\n                        opacity: 1\n                    }\n                }\n            }\n        }\n    },\n    MuiChip: {\n        styleOverrides: {\n            root: {\n                borderRadius: \"12px\",\n                backdropFilter: \"blur(20px)\",\n                background: \"rgba(255, 255, 255, 0.1)\",\n                border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                color: \"white\",\n                fontWeight: 500,\n                transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n                \"&:hover\": {\n                    background: \"rgba(255, 255, 255, 0.2)\",\n                    transform: \"translateY(-1px)\"\n                }\n            }\n        }\n    },\n    MuiIconButton: {\n        styleOverrides: {\n            root: {\n                borderRadius: \"12px\",\n                backdropFilter: \"blur(20px)\",\n                background: \"rgba(255, 255, 255, 0.1)\",\n                border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                color: \"white\",\n                transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n                \"&:hover\": {\n                    background: \"rgba(255, 255, 255, 0.2)\",\n                    transform: \"translateY(-2px)\",\n                    boxShadow: \"0 10px 20px rgba(0, 0, 0, 0.1)\"\n                }\n            }\n        }\n    },\n    MuiDrawer: {\n        styleOverrides: {\n            paper: {\n                backdropFilter: \"blur(20px)\",\n                background: \"rgba(255, 255, 255, 0.05)\",\n                border: \"1px solid rgba(255, 255, 255, 0.1)\",\n                borderLeft: \"none\"\n            }\n        }\n    },\n    MuiAppBar: {\n        styleOverrides: {\n            root: {\n                backdropFilter: \"blur(20px)\",\n                background: \"rgba(255, 255, 255, 0.1)\",\n                border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                borderTop: \"none\",\n                borderLeft: \"none\",\n                borderRight: \"none\",\n                boxShadow: \"0 10px 30px rgba(0, 0, 0, 0.1)\"\n            }\n        }\n    }\n};\n// Create the theme\nconst themeOptions = {\n    palette,\n    typography,\n    spacing,\n    breakpoints,\n    components,\n    transitions: {\n        duration: {\n            shortest: 150,\n            shorter: 200,\n            short: 250,\n            standard: 300,\n            complex: 375,\n            enteringScreen: 225,\n            leavingScreen: 195\n        },\n        easing: {\n            easeInOut: \"cubic-bezier(0.4, 0, 0.2, 1)\",\n            easeOut: \"cubic-bezier(0.0, 0, 0.2, 1)\",\n            easeIn: \"cubic-bezier(0.4, 0, 1, 1)\",\n            sharp: \"cubic-bezier(0.4, 0, 0.6, 1)\"\n        }\n    },\n    shadows: [\n        \"none\",\n        \"0 2px 8px rgba(0, 0, 0, 0.1)\",\n        \"0 4px 16px rgba(0, 0, 0, 0.1)\",\n        \"0 8px 24px rgba(0, 0, 0, 0.15)\",\n        \"0 12px 32px rgba(0, 0, 0, 0.15)\",\n        \"0 16px 40px rgba(0, 0, 0, 0.2)\",\n        \"0 20px 48px rgba(0, 0, 0, 0.2)\",\n        \"0 24px 56px rgba(0, 0, 0, 0.25)\",\n        \"0 28px 64px rgba(0, 0, 0, 0.25)\",\n        \"0 32px 72px rgba(0, 0, 0, 0.3)\",\n        \"0 36px 80px rgba(0, 0, 0, 0.3)\",\n        \"0 40px 88px rgba(0, 0, 0, 0.35)\",\n        \"0 44px 96px rgba(0, 0, 0, 0.35)\",\n        \"0 48px 104px rgba(0, 0, 0, 0.4)\",\n        \"0 52px 112px rgba(0, 0, 0, 0.4)\",\n        \"0 56px 120px rgba(0, 0, 0, 0.45)\",\n        \"0 60px 128px rgba(0, 0, 0, 0.45)\",\n        \"0 64px 136px rgba(0, 0, 0, 0.5)\",\n        \"0 68px 144px rgba(0, 0, 0, 0.5)\",\n        \"0 72px 152px rgba(0, 0, 0, 0.55)\",\n        \"0 76px 160px rgba(0, 0, 0, 0.55)\",\n        \"0 80px 168px rgba(0, 0, 0, 0.6)\",\n        \"0 84px 176px rgba(0, 0, 0, 0.6)\",\n        \"0 88px 184px rgba(0, 0, 0, 0.65)\",\n        \"0 92px 192px rgba(0, 0, 0, 0.65)\"\n    ]\n};\nconst theme = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(themeOptions);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/theme/theme.ts\n");

/***/ }),

/***/ "(rsc)/./app/components/AnimatedBackground.tsx":
/*!***********************************************!*\
  !*** ./app/components/AnimatedBackground.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\Projects\Chat\app\components\AnimatedBackground.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/components/ThemeProvider.tsx":
/*!******************************************!*\
  !*** ./app/components/ThemeProvider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\Projects\Chat\app\components\ThemeProvider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ThemeProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./components/ThemeProvider */ \"(rsc)/./app/components/ThemeProvider.tsx\");\n/* harmony import */ var _components_AnimatedBackground__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/AnimatedBackground */ \"(rsc)/./app/components/AnimatedBackground.tsx\");\n\n\n\nconst metadata = {\n    title: \"Ollama MCP Chat\",\n    description: \"A dynamic chat interface for Ollama LLM with integrated Smithery.ai MCP tool servers\",\n    keywords: [\n        \"ollama\",\n        \"mcp\",\n        \"chat\",\n        \"ai\",\n        \"llm\",\n        \"smithery\"\n    ],\n    authors: [\n        {\n            name: \"Ollama MCP Chat\"\n        }\n    ]\n};\nconst viewport = {\n    width: \"device-width\",\n    initialScale: 1\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\layout.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\layout.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\layout.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\layout.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeProvider__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AnimatedBackground__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\layout.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, this),\n                        children\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\layout.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\layout.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\layout.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\Projects\Chat\app\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@mui","vendor-chunks/framer-motion","vendor-chunks/@popperjs","vendor-chunks/@emotion","vendor-chunks/@babel","vendor-chunks/stylis","vendor-chunks/react-transition-group","vendor-chunks/prop-types","vendor-chunks/@swc","vendor-chunks/uuid","vendor-chunks/hoist-non-react-statics","vendor-chunks/clsx","vendor-chunks/react-is","vendor-chunks/object-assign"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();