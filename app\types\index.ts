// Core chat message types
export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'tool';
  content: string;
  timestamp: Date;
  attachments?: FileAttachment[];
  toolCall?: ToolCall;
  toolResult?: ToolResult;
}

// File attachment types
export interface FileAttachment {
  id: string;
  name: string;
  type: 'image' | 'document';
  url: string;
  mimeType: string;
  size: number;
}

// Ollama model types
export interface OllamaModel {
  name: string;
  model: string;
  size: number;
  digest: string;
  details: {
    format: string;
    family: string;
    families: string[];
    parameter_size: string;
    quantization_level: string;
  };
  capabilities: ModelCapabilities;
}

export interface ModelCapabilities {
  supportsTools: boolean;
  supportsVision: boolean;
  supportsCode: boolean;
  contextLength?: number;
}

// Tool call types for MCP integration
export interface ToolCall {
  id: string;
  type: 'function';
  function: {
    name: string;
    arguments: string;
  };
}

export interface ToolResult {
  toolCallId: string;
  result: any;
  error?: string;
}

// MCP server configuration
export interface MCPServerConfig {
  command: string;
  args: string[];
  description?: string;
  env?: Record<string, string>;
}

export interface MCPConfig {
  mcpServers: Record<string, MCPServerConfig>;
}

// Tool definition for Ollama
export interface Tool {
  type: 'function';
  function: {
    name: string;
    description: string;
    parameters: {
      type: 'object';
      properties: Record<string, any>;
      required?: string[];
    };
  };
}

// API response types
export interface OllamaResponse {
  model: string;
  created_at: string;
  message: {
    role: string;
    content: string;
    tool_calls?: ToolCall[];
  };
  done: boolean;
}

// UI state types
export interface ChatState {
  messages: ChatMessage[];
  selectedModel: string | null;
  availableModels: OllamaModel[];
  isLoading: boolean;
  mcpServers: Record<string, any>;
  availableTools: Tool[];
}
