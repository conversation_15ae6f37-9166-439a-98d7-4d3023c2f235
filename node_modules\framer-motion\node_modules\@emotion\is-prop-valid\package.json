{"name": "@emotion/is-prop-valid", "version": "0.8.8", "description": "A function to check whether a prop is valid for HTML and SVG elements", "main": "dist/is-prop-valid.cjs.js", "module": "dist/is-prop-valid.esm.js", "types": "types/index.d.ts", "license": "MIT", "repository": "https://github.com/emotion-js/emotion/tree/master/packages/is-prop-valid", "scripts": {"test:typescript": "dtslint types"}, "publishConfig": {"access": "public"}, "dependencies": {"@emotion/memoize": "0.7.4"}, "devDependencies": {"dtslint": "^0.3.0"}, "files": ["src", "dist", "types"], "browser": {"./dist/is-prop-valid.cjs.js": "./dist/is-prop-valid.browser.cjs.js", "./dist/is-prop-valid.esm.js": "./dist/is-prop-valid.browser.esm.js"}}