'use client';

import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Chip,
  Stack,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  IconButton,
  Tooltip,
  useTheme,
  alpha,
  Divider,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Build as BuildIcon,
  Psychology as PsychologyIcon,
  Code as CodeIcon,
  Search as SearchIcon,
  Language as LanguageIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { Tool } from '@/types';

interface ToolCapabilitiesPanelProps {
  tools: Tool[];
  onRefresh?: () => void;
}

const getToolIcon = (toolName: string) => {
  if (toolName.includes('search')) return <SearchIcon />;
  if (toolName.includes('code') || toolName.includes('docs')) return <CodeIcon />;
  if (toolName.includes('web')) return <LanguageIcon />;
  return <BuildIcon />;
};

const getServerColor = (serverName?: string) => {
  switch (serverName) {
    case 'context7': return 'primary';
    case 'exa': return 'secondary';
    default: return 'default';
  }
};

export default function ToolCapabilitiesPanel({ tools, onRefresh }: ToolCapabilitiesPanelProps) {
  const [expanded, setExpanded] = useState<string | false>(false);
  const theme = useTheme();

  const handleChange = (panel: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpanded(isExpanded ? panel : false);
  };

  const groupedTools = tools.reduce((acc, tool) => {
    const serverName = tool.serverName || 'unknown';
    if (!acc[serverName]) {
      acc[serverName] = [];
    }
    acc[serverName].push(tool);
    return acc;
  }, {} as Record<string, Tool[]>);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Paper
        elevation={3}
        sx={{
          borderRadius: 3,
          overflow: 'hidden',
          background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.secondary.main, 0.05)} 100%)`,
          border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
        }}
      >
        {/* Header */}
        <Box
          sx={{
            p: 3,
            background: alpha(theme.palette.background.paper, 0.8),
            backdropFilter: 'blur(20px)',
          }}
        >
          <Stack direction="row" alignItems="center" justifyContent="space-between">
            <Stack direction="row" alignItems="center" spacing={2}>
              <PsychologyIcon color="primary" sx={{ fontSize: 28 }} />
              <Box>
                <Typography variant="h6" fontWeight={600}>
                  AI Tool Capabilities
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {tools.length} tools available across {Object.keys(groupedTools).length} servers
                </Typography>
              </Box>
            </Stack>
            {onRefresh && (
              <Tooltip title="Refresh Tools">
                <IconButton onClick={onRefresh} color="primary">
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
            )}
          </Stack>
        </Box>

        <Divider />

        {/* Tools by Server */}
        <Box sx={{ p: 2 }}>
          {Object.keys(groupedTools).length === 0 ? (
            <Box
              sx={{
                textAlign: 'center',
                py: 4,
                color: 'text.secondary',
              }}
            >
              <BuildIcon sx={{ fontSize: 48, mb: 2, opacity: 0.5 }} />
              <Typography variant="h6" gutterBottom>
                No Tools Available
              </Typography>
              <Typography variant="body2">
                Start MCP servers to see available tools
              </Typography>
            </Box>
          ) : (
            <Stack spacing={1}>
              {Object.entries(groupedTools).map(([serverName, serverTools]) => (
                <motion.div
                  key={serverName}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <Accordion
                    expanded={expanded === serverName}
                    onChange={handleChange(serverName)}
                    sx={{
                      borderRadius: 2,
                      '&:before': { display: 'none' },
                      boxShadow: 'none',
                      border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                      '&.Mui-expanded': {
                        margin: 0,
                      },
                    }}
                  >
                    <AccordionSummary
                      expandIcon={<ExpandMoreIcon />}
                      sx={{
                        borderRadius: 2,
                        '&.Mui-expanded': {
                          borderBottomLeftRadius: 0,
                          borderBottomRightRadius: 0,
                        },
                      }}
                    >
                      <Stack direction="row" alignItems="center" spacing={2} width="100%">
                        <Chip
                          label={serverName}
                          color={getServerColor(serverName) as any}
                          size="small"
                          variant="outlined"
                        />
                        <Typography variant="subtitle1" fontWeight={500}>
                          {serverTools.length} tool{serverTools.length !== 1 ? 's' : ''}
                        </Typography>
                      </Stack>
                    </AccordionSummary>
                    <AccordionDetails sx={{ pt: 0 }}>
                      <Stack spacing={2}>
                        {serverTools.map((tool, index) => (
                          <motion.div
                            key={tool.function.name}
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: index * 0.1 }}
                          >
                            <Paper
                              elevation={1}
                              sx={{
                                p: 2,
                                borderRadius: 2,
                                background: alpha(theme.palette.background.paper, 0.5),
                                border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                              }}
                            >
                              <Stack direction="row" alignItems="flex-start" spacing={2}>
                                <Box
                                  sx={{
                                    p: 1,
                                    borderRadius: 1,
                                    background: alpha(theme.palette.primary.main, 0.1),
                                    color: 'primary.main',
                                  }}
                                >
                                  {getToolIcon(tool.function.name)}
                                </Box>
                                <Box flex={1}>
                                  <Typography variant="subtitle2" fontWeight={600} gutterBottom>
                                    {tool.function.name}
                                  </Typography>
                                  <Typography variant="body2" color="text.secondary" gutterBottom>
                                    {tool.function.description}
                                  </Typography>
                                  {tool.function.parameters.required && tool.function.parameters.required.length > 0 && (
                                    <Stack direction="row" spacing={1} mt={1}>
                                      <Typography variant="caption" color="text.secondary">
                                        Required:
                                      </Typography>
                                      {tool.function.parameters.required.map((param) => (
                                        <Chip
                                          key={param}
                                          label={param}
                                          size="small"
                                          variant="outlined"
                                          sx={{ height: 20, fontSize: '0.7rem' }}
                                        />
                                      ))}
                                    </Stack>
                                  )}
                                </Box>
                              </Stack>
                            </Paper>
                          </motion.div>
                        ))}
                      </Stack>
                    </AccordionDetails>
                  </Accordion>
                </motion.div>
              ))}
            </Stack>
          )}
        </Box>
      </Paper>
    </motion.div>
  );
}
