"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/mcp/route";
exports.ids = ["app/api/mcp/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fmcp%2Froute&page=%2Fapi%2Fmcp%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmcp%2Froute.ts&appDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fmcp%2Froute&page=%2Fapi%2Fmcp%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmcp%2Froute.ts&appDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_20021792_Documents_Projects_Chat_app_api_mcp_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/mcp/route.ts */ \"(rsc)/./app/api/mcp/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/mcp/route\",\n        pathname: \"/api/mcp\",\n        filename: \"route\",\n        bundlePath: \"app/api/mcp/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\api\\\\mcp\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_20021792_Documents_Projects_Chat_app_api_mcp_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/mcp/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fmcp%2Froute&page=%2Fapi%2Fmcp%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmcp%2Froute.ts&appDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/mcp/route.ts":
/*!******************************!*\
  !*** ./app/api/mcp/route.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n// Store active MCP server processes\nconst activeServers = new Map();\n// GET /api/mcp - Get MCP configuration and available tools\nasync function GET() {\n    try {\n        const configPath = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), \"public\", \"mcp.config.json\");\n        if (!fs__WEBPACK_IMPORTED_MODULE_1___default().existsSync(configPath)) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                mcpServers: {},\n                tools: []\n            });\n        }\n        const configData = fs__WEBPACK_IMPORTED_MODULE_1___default().readFileSync(configPath, \"utf8\");\n        const config = JSON.parse(configData);\n        // Get available tools from all configured servers\n        const tools = await getAvailableTools(config);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            mcpServers: config.mcpServers,\n            tools\n        });\n    } catch (error) {\n        console.error(\"Error reading MCP config:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to read MCP configuration\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/mcp - Execute tool call or update configuration\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { action, serverName, toolName, arguments: toolArgs, config } = body;\n        switch(action){\n            case \"execute_tool\":\n                return await executeToolCall(serverName, toolName, toolArgs);\n            case \"update_config\":\n                return await updateMCPConfig(config);\n            case \"start_server\":\n                return await startMCPServer(serverName);\n            case \"stop_server\":\n                return await stopMCPServer(serverName);\n            default:\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"Invalid action\"\n                }, {\n                    status: 400\n                });\n        }\n    } catch (error) {\n        console.error(\"Error in MCP API:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to process MCP request\"\n        }, {\n            status: 500\n        });\n    }\n}\n// Helper function to get available tools from MCP servers\nasync function getAvailableTools(config) {\n    const tools = [];\n    console.log(\"Loading tools from MCP config:\", config);\n    // Check each configured MCP server and add their tools\n    for (const [serverName, serverConfig] of Object.entries(config.mcpServers || {})){\n        console.log(`Processing server: ${serverName}`, serverConfig);\n        try {\n            // For now, we'll define known tools for each server type\n            // In a real implementation, you would start the MCP server and query its capabilities\n            if (serverName === \"exa\" || serverConfig.description?.toLowerCase().includes(\"exa\")) {\n                console.log(\"Adding Exa search tools\");\n                tools.push({\n                    type: \"function\",\n                    function: {\n                        name: \"search_web\",\n                        description: \"Search the web using Exa search engine\",\n                        parameters: {\n                            type: \"object\",\n                            properties: {\n                                query: {\n                                    type: \"string\",\n                                    description: \"The search query to execute\"\n                                },\n                                num_results: {\n                                    type: \"number\",\n                                    description: \"Number of results to return (default: 10)\",\n                                    default: 10\n                                },\n                                include_domains: {\n                                    type: \"array\",\n                                    items: {\n                                        type: \"string\"\n                                    },\n                                    description: \"Domains to include in search\"\n                                },\n                                exclude_domains: {\n                                    type: \"array\",\n                                    items: {\n                                        type: \"string\"\n                                    },\n                                    description: \"Domains to exclude from search\"\n                                }\n                            },\n                            required: [\n                                \"query\"\n                            ]\n                        }\n                    }\n                });\n            }\n            // Add other server types as needed\n            if (serverName === \"weather\" || serverConfig.description?.toLowerCase().includes(\"weather\")) {\n                console.log(\"Adding weather tools\");\n                tools.push({\n                    type: \"function\",\n                    function: {\n                        name: \"get_weather\",\n                        description: \"Get current weather information for a city\",\n                        parameters: {\n                            type: \"object\",\n                            properties: {\n                                city: {\n                                    type: \"string\",\n                                    description: \"The name of the city to get weather for\"\n                                },\n                                unit: {\n                                    type: \"string\",\n                                    enum: [\n                                        \"celsius\",\n                                        \"fahrenheit\"\n                                    ],\n                                    description: \"Temperature unit (default: celsius)\",\n                                    default: \"celsius\"\n                                }\n                            },\n                            required: [\n                                \"city\"\n                            ]\n                        }\n                    }\n                });\n            }\n        } catch (error) {\n            console.error(`Error loading tools for server ${serverName}:`, error);\n        }\n    }\n    // Always add weather tool for demonstration (since it's simulated)\n    if (!tools.some((tool)=>tool.function.name === \"get_weather\")) {\n        console.log(\"Adding default weather tool\");\n        tools.push({\n            type: \"function\",\n            function: {\n                name: \"get_weather\",\n                description: \"Get current weather information for a city\",\n                parameters: {\n                    type: \"object\",\n                    properties: {\n                        city: {\n                            type: \"string\",\n                            description: \"The name of the city to get weather for\"\n                        },\n                        unit: {\n                            type: \"string\",\n                            enum: [\n                                \"celsius\",\n                                \"fahrenheit\"\n                            ],\n                            description: \"Temperature unit (default: celsius)\",\n                            default: \"celsius\"\n                        }\n                    },\n                    required: [\n                        \"city\"\n                    ]\n                }\n            }\n        });\n    }\n    console.log(`Loaded ${tools.length} tools:`, tools.map((t)=>t.function.name));\n    return tools;\n}\n// Execute a tool call on an MCP server\nasync function executeToolCall(serverName, toolName, toolArgs) {\n    try {\n        console.log(`Executing tool call: ${serverName}.${toolName}`, toolArgs);\n        if (serverName === \"weather\" && toolName === \"get_weather\") {\n            // Simulate weather API call\n            const weatherData = {\n                city: toolArgs.city,\n                temperature: Math.round(Math.random() * 30 + 5),\n                unit: toolArgs.unit || \"celsius\",\n                condition: [\n                    \"sunny\",\n                    \"cloudy\",\n                    \"rainy\",\n                    \"snowy\"\n                ][Math.floor(Math.random() * 4)],\n                humidity: Math.round(Math.random() * 100),\n                windSpeed: Math.round(Math.random() * 20),\n                description: `Current weather in ${toolArgs.city}`,\n                timestamp: new Date().toISOString()\n            };\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: true,\n                result: weatherData\n            });\n        }\n        if (serverName === \"exa\" && toolName === \"search_web\") {\n            // Try to use actual Exa API if available, otherwise simulate\n            try {\n                // Check if we have Exa API key in environment\n                const exaApiKey = process.env.EXA_API_KEY;\n                if (exaApiKey) {\n                    // Make actual Exa API call\n                    const exaResponse = await fetch(\"https://api.exa.ai/search\", {\n                        method: \"POST\",\n                        headers: {\n                            \"Content-Type\": \"application/json\",\n                            \"x-api-key\": exaApiKey\n                        },\n                        body: JSON.stringify({\n                            query: toolArgs.query,\n                            numResults: toolArgs.num_results || 10,\n                            includeDomains: toolArgs.include_domains,\n                            excludeDomains: toolArgs.exclude_domains,\n                            useAutoprompt: true,\n                            type: \"search\"\n                        })\n                    });\n                    if (exaResponse.ok) {\n                        const exaData = await exaResponse.json();\n                        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                            success: true,\n                            result: {\n                                results: exaData.results.map((result)=>({\n                                        title: result.title,\n                                        url: result.url,\n                                        snippet: result.text || result.snippet || \"No snippet available\",\n                                        publishedDate: result.publishedDate,\n                                        score: result.score\n                                    })),\n                                query: toolArgs.query,\n                                num_results: exaData.results.length\n                            }\n                        });\n                    }\n                }\n            } catch (exaError) {\n                console.warn(\"Exa API call failed, falling back to simulation:\", exaError);\n            }\n            // Fallback to simulation with more realistic data\n            const simulatedResults = {\n                results: [\n                    {\n                        title: `Search Results for \"${toolArgs.query}\"`,\n                        url: \"https://example.com/search-result-1\",\n                        snippet: `This is a simulated search result for the query: \"${toolArgs.query}\". In a real implementation, this would contain actual web search results from the Exa search engine.`,\n                        publishedDate: new Date().toISOString(),\n                        score: 0.95\n                    },\n                    {\n                        title: `Related Information about ${toolArgs.query}`,\n                        url: \"https://example.com/search-result-2\",\n                        snippet: `Additional context and information related to \"${toolArgs.query}\". This demonstrates how the search tool would return multiple relevant results.`,\n                        publishedDate: new Date(Date.now() - 86400000).toISOString(),\n                        score: 0.87\n                    }\n                ],\n                query: toolArgs.query,\n                num_results: toolArgs.num_results || 10,\n                note: \"This is a simulated response. Configure EXA_API_KEY environment variable for real search results.\"\n            };\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: true,\n                result: simulatedResults\n            });\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: `Unknown tool: ${toolName} for server: ${serverName}`\n        }, {\n            status: 400\n        });\n    } catch (error) {\n        console.error(\"Error executing tool call:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to execute tool call\"\n        }, {\n            status: 500\n        });\n    }\n}\n// Update MCP configuration\nasync function updateMCPConfig(config) {\n    try {\n        const configPath = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), \"public\", \"mcp.config.json\");\n        fs__WEBPACK_IMPORTED_MODULE_1___default().writeFileSync(configPath, JSON.stringify(config, null, 2));\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true\n        });\n    } catch (error) {\n        console.error(\"Error updating MCP config:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to update configuration\"\n        }, {\n            status: 500\n        });\n    }\n}\n// Start an MCP server\nasync function startMCPServer(serverName) {\n    try {\n        // Implementation would start the actual MCP server process\n        // For now, we'll just simulate success\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            message: `Server ${serverName} started`\n        });\n    } catch (error) {\n        console.error(\"Error starting MCP server:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to start server\"\n        }, {\n            status: 500\n        });\n    }\n}\n// Stop an MCP server\nasync function stopMCPServer(serverName) {\n    try {\n        const process1 = activeServers.get(serverName);\n        if (process1) {\n            process1.kill();\n            activeServers.delete(serverName);\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            message: `Server ${serverName} stopped`\n        });\n    } catch (error) {\n        console.error(\"Error stopping MCP server:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to stop server\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/mcp/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fmcp%2Froute&page=%2Fapi%2Fmcp%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmcp%2Froute.ts&appDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();