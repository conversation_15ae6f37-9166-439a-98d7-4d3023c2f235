/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/mcp/route";
exports.ids = ["app/api/mcp/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmcp%2Froute&page=%2Fapi%2Fmcp%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmcp%2Froute.ts&appDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmcp%2Froute&page=%2Fapi%2Fmcp%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmcp%2Froute.ts&appDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_20021792_Documents_Projects_Chat_app_api_mcp_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/mcp/route.ts */ \"(rsc)/./app/api/mcp/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/mcp/route\",\n        pathname: \"/api/mcp\",\n        filename: \"route\",\n        bundlePath: \"app/api/mcp/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\api\\\\mcp\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_20021792_Documents_Projects_Chat_app_api_mcp_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmcp%2Froute&page=%2Fapi%2Fmcp%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmcp%2Froute.ts&appDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./app/api/mcp/route.ts":
/*!******************************!*\
  !*** ./app/api/mcp/route.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_mcpClient__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/mcpClient */ \"(rsc)/./app/lib/mcpClient.ts\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\n// GET /api/mcp - Get MCP configuration and available tools\nasync function GET() {\n    try {\n        const configPath = path__WEBPACK_IMPORTED_MODULE_3___default().join(process.cwd(), 'public', 'mcp.config.json');\n        if (!fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(configPath)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                mcpServers: {},\n                tools: []\n            });\n        }\n        const configData = fs__WEBPACK_IMPORTED_MODULE_2___default().readFileSync(configPath, 'utf8');\n        const config = JSON.parse(configData);\n        // Start servers if not already running and get tools\n        await initializeMCPServers(config);\n        const tools = await _lib_mcpClient__WEBPACK_IMPORTED_MODULE_1__.mcpClient.getAvailableTools();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            mcpServers: config.mcpServers,\n            tools\n        });\n    } catch (error) {\n        console.error('Error reading MCP config:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to read MCP configuration'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/mcp - Execute tool call or update configuration\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { action, serverName, toolName, arguments: toolArgs, config } = body;\n        switch(action){\n            case 'execute_tool':\n                return await executeToolCall(serverName, toolName, toolArgs);\n            case 'update_config':\n                return await updateMCPConfig(config);\n            case 'start_server':\n                return await startMCPServer(serverName, config);\n            case 'stop_server':\n                return await stopMCPServer(serverName);\n            default:\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Invalid action'\n                }, {\n                    status: 400\n                });\n        }\n    } catch (error) {\n        console.error('Error in MCP API:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to process MCP request'\n        }, {\n            status: 500\n        });\n    }\n}\n// Initialize MCP servers from config\nasync function initializeMCPServers(config) {\n    for (const [serverName, serverConfig] of Object.entries(config.mcpServers || {})){\n        if (!_lib_mcpClient__WEBPACK_IMPORTED_MODULE_1__.mcpClient.servers.has(serverName)) {\n            try {\n                console.log(`Starting MCP server: ${serverName}`);\n                await _lib_mcpClient__WEBPACK_IMPORTED_MODULE_1__.mcpClient.startServer(serverName, serverConfig);\n            } catch (error) {\n                console.error(`Failed to start MCP server ${serverName}:`, error);\n            }\n        }\n    }\n}\n// Execute a tool call using MCP client\nasync function executeToolCall(serverName, toolName, toolArgs) {\n    try {\n        console.log(`Executing tool ${toolName} on server ${serverName} with args:`, toolArgs);\n        const result = await _lib_mcpClient__WEBPACK_IMPORTED_MODULE_1__.mcpClient.executeToolCall(serverName, toolName, toolArgs);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            result: result.content || result\n        });\n    } catch (error) {\n        console.error('Error executing tool call:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: `Failed to execute tool call: ${error.message}`\n        }, {\n            status: 500\n        });\n    }\n}\n// Update MCP configuration\nasync function updateMCPConfig(config) {\n    try {\n        const configPath = path__WEBPACK_IMPORTED_MODULE_3___default().join(process.cwd(), 'public', 'mcp.config.json');\n        fs__WEBPACK_IMPORTED_MODULE_2___default().writeFileSync(configPath, JSON.stringify(config, null, 2));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true\n        });\n    } catch (error) {\n        console.error('Error updating MCP config:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to update configuration'\n        }, {\n            status: 500\n        });\n    }\n}\n// Start an MCP server\nasync function startMCPServer(serverName, config) {\n    try {\n        const configPath = path__WEBPACK_IMPORTED_MODULE_3___default().join(process.cwd(), 'public', 'mcp.config.json');\n        const configData = fs__WEBPACK_IMPORTED_MODULE_2___default().readFileSync(configPath, 'utf8');\n        const mcpConfig = JSON.parse(configData);\n        const serverConfig = mcpConfig.mcpServers[serverName];\n        if (!serverConfig) {\n            throw new Error(`Server ${serverName} not found in configuration`);\n        }\n        await _lib_mcpClient__WEBPACK_IMPORTED_MODULE_1__.mcpClient.startServer(serverName, serverConfig);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: `Server ${serverName} started successfully`\n        });\n    } catch (error) {\n        console.error('Error starting MCP server:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: `Failed to start server: ${error.message}`\n        }, {\n            status: 500\n        });\n    }\n}\n// Stop an MCP server\nasync function stopMCPServer(serverName) {\n    try {\n        await _lib_mcpClient__WEBPACK_IMPORTED_MODULE_1__.mcpClient.stopServer(serverName);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: `Server ${serverName} stopped successfully`\n        });\n    } catch (error) {\n        console.error('Error stopping MCP server:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: `Failed to stop server: ${error.message}`\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/mcp/route.ts\n");

/***/ }),

/***/ "(rsc)/./app/lib/mcpClient.ts":
/*!******************************!*\
  !*** ./app/lib/mcpClient.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MCPClient: () => (/* binding */ MCPClient),\n/* harmony export */   mcpClient: () => (/* binding */ mcpClient)\n/* harmony export */ });\nclass MCPClient {\n    async getAvailableTools() {\n        const allTools = [];\n        // Return demo tools for now\n        for (const [serverName, server] of this.servers){\n            if (server.isConnected) {\n                allTools.push(...server.tools);\n            }\n        }\n        return allTools;\n    }\n    async executeToolCall(serverName, toolName, args) {\n        const server = this.servers.get(serverName);\n        if (!server || !server.isConnected) {\n            throw new Error(`Server ${serverName} is not connected`);\n        }\n        // Simulate tool execution for demo\n        console.log(`Executing ${toolName} on ${serverName} with args:`, args);\n        // Return demo response based on tool\n        if (toolName === 'search_web') {\n            return {\n                content: [\n                    {\n                        type: 'text',\n                        text: `Search results for \"${args.query}\": This is a demo response from the ${serverName} server.`\n                    }\n                ]\n            };\n        }\n        return {\n            content: [\n                {\n                    type: 'text',\n                    text: `Tool ${toolName} executed successfully on ${serverName} server.`\n                }\n            ]\n        };\n    }\n    async startServer(serverName, config) {\n        if (this.servers.has(serverName)) {\n            await this.stopServer(serverName);\n        }\n        try {\n            // Create demo tools based on server type\n            let demoTools = [];\n            if (serverName === 'context7') {\n                demoTools = [\n                    {\n                        type: 'function',\n                        function: {\n                            name: 'search_docs',\n                            description: 'Search through documentation and code context',\n                            parameters: {\n                                type: 'object',\n                                properties: {\n                                    query: {\n                                        type: 'string',\n                                        description: 'Search query'\n                                    },\n                                    context: {\n                                        type: 'string',\n                                        description: 'Context type (docs, code, etc.)'\n                                    }\n                                },\n                                required: [\n                                    'query'\n                                ]\n                            }\n                        },\n                        serverName\n                    }\n                ];\n            } else if (serverName === 'exa') {\n                demoTools = [\n                    {\n                        type: 'function',\n                        function: {\n                            name: 'search_web',\n                            description: 'Search the web using Exa API',\n                            parameters: {\n                                type: 'object',\n                                properties: {\n                                    query: {\n                                        type: 'string',\n                                        description: 'Search query'\n                                    },\n                                    num_results: {\n                                        type: 'number',\n                                        description: 'Number of results',\n                                        default: 10\n                                    }\n                                },\n                                required: [\n                                    'query'\n                                ]\n                            }\n                        },\n                        serverName\n                    }\n                ];\n            }\n            const serverInstance = {\n                name: serverName,\n                config,\n                tools: demoTools,\n                isConnected: true\n            };\n            this.servers.set(serverName, serverInstance);\n            console.log(`MCP server ${serverName} started successfully (demo mode)`);\n        } catch (error) {\n            console.error(`Failed to start MCP server ${serverName}:`, error);\n            throw error;\n        }\n    }\n    async stopServer(serverName) {\n        const server = this.servers.get(serverName);\n        if (!server) {\n            return;\n        }\n        try {\n            this.servers.delete(serverName);\n            console.log(`MCP server ${serverName} stopped`);\n        } catch (error) {\n            console.error(`Error stopping MCP server ${serverName}:`, error);\n            this.servers.delete(serverName);\n        }\n    }\n    async stopAllServers() {\n        const stopPromises = Array.from(this.servers.keys()).map((serverName)=>this.stopServer(serverName));\n        await Promise.all(stopPromises);\n    }\n    constructor(){\n        this.servers = new Map();\n    }\n}\n// Singleton instance\nconst mcpClient = new MCPClient();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGliL21jcENsaWVudC50cyIsIm1hcHBpbmdzIjoiOzs7OztBQXFCTyxNQUFNQTtJQUdYLE1BQU1DLG9CQUFxQztRQUN6QyxNQUFNQyxXQUFtQixFQUFFO1FBRTNCLDRCQUE0QjtRQUM1QixLQUFLLE1BQU0sQ0FBQ0MsWUFBWUMsT0FBTyxJQUFJLElBQUksQ0FBQ0MsT0FBTyxDQUFFO1lBQy9DLElBQUlELE9BQU9FLFdBQVcsRUFBRTtnQkFDdEJKLFNBQVNLLElBQUksSUFBSUgsT0FBT0ksS0FBSztZQUMvQjtRQUNGO1FBRUEsT0FBT047SUFDVDtJQUVBLE1BQU1PLGdCQUFnQk4sVUFBa0IsRUFBRU8sUUFBZ0IsRUFBRUMsSUFBUyxFQUFnQjtRQUNuRixNQUFNUCxTQUFTLElBQUksQ0FBQ0MsT0FBTyxDQUFDTyxHQUFHLENBQUNUO1FBQ2hDLElBQUksQ0FBQ0MsVUFBVSxDQUFDQSxPQUFPRSxXQUFXLEVBQUU7WUFDbEMsTUFBTSxJQUFJTyxNQUFNLENBQUMsT0FBTyxFQUFFVixXQUFXLGlCQUFpQixDQUFDO1FBQ3pEO1FBRUEsbUNBQW1DO1FBQ25DVyxRQUFRQyxHQUFHLENBQUMsQ0FBQyxVQUFVLEVBQUVMLFNBQVMsSUFBSSxFQUFFUCxXQUFXLFdBQVcsQ0FBQyxFQUFFUTtRQUVqRSxxQ0FBcUM7UUFDckMsSUFBSUQsYUFBYSxjQUFjO1lBQzdCLE9BQU87Z0JBQ0xNLFNBQVM7b0JBQUM7d0JBQ1JDLE1BQU07d0JBQ05DLE1BQU0sQ0FBQyxvQkFBb0IsRUFBRVAsS0FBS1EsS0FBSyxDQUFDLG9DQUFvQyxFQUFFaEIsV0FBVyxRQUFRLENBQUM7b0JBQ3BHO2lCQUFFO1lBQ0o7UUFDRjtRQUVBLE9BQU87WUFDTGEsU0FBUztnQkFBQztvQkFDUkMsTUFBTTtvQkFDTkMsTUFBTSxDQUFDLEtBQUssRUFBRVIsU0FBUywwQkFBMEIsRUFBRVAsV0FBVyxRQUFRLENBQUM7Z0JBQ3pFO2FBQUU7UUFDSjtJQUNGO0lBRUEsTUFBTWlCLFlBQVlqQixVQUFrQixFQUFFa0IsTUFBdUIsRUFBaUI7UUFDNUUsSUFBSSxJQUFJLENBQUNoQixPQUFPLENBQUNpQixHQUFHLENBQUNuQixhQUFhO1lBQ2hDLE1BQU0sSUFBSSxDQUFDb0IsVUFBVSxDQUFDcEI7UUFDeEI7UUFFQSxJQUFJO1lBQ0YseUNBQXlDO1lBQ3pDLElBQUlxQixZQUFvQixFQUFFO1lBRTFCLElBQUlyQixlQUFlLFlBQVk7Z0JBQzdCcUIsWUFBWTtvQkFDVjt3QkFDRVAsTUFBTTt3QkFDTlEsVUFBVTs0QkFDUkMsTUFBTTs0QkFDTkMsYUFBYTs0QkFDYkMsWUFBWTtnQ0FDVlgsTUFBTTtnQ0FDTlksWUFBWTtvQ0FDVlYsT0FBTzt3Q0FBRUYsTUFBTTt3Q0FBVVUsYUFBYTtvQ0FBZTtvQ0FDckRHLFNBQVM7d0NBQUViLE1BQU07d0NBQVVVLGFBQWE7b0NBQWtDO2dDQUM1RTtnQ0FDQUksVUFBVTtvQ0FBQztpQ0FBUTs0QkFDckI7d0JBQ0Y7d0JBQ0E1QjtvQkFDRjtpQkFDRDtZQUNILE9BQU8sSUFBSUEsZUFBZSxPQUFPO2dCQUMvQnFCLFlBQVk7b0JBQ1Y7d0JBQ0VQLE1BQU07d0JBQ05RLFVBQVU7NEJBQ1JDLE1BQU07NEJBQ05DLGFBQWE7NEJBQ2JDLFlBQVk7Z0NBQ1ZYLE1BQU07Z0NBQ05ZLFlBQVk7b0NBQ1ZWLE9BQU87d0NBQUVGLE1BQU07d0NBQVVVLGFBQWE7b0NBQWU7b0NBQ3JESyxhQUFhO3dDQUFFZixNQUFNO3dDQUFVVSxhQUFhO3dDQUFxQk0sU0FBUztvQ0FBRztnQ0FDL0U7Z0NBQ0FGLFVBQVU7b0NBQUM7aUNBQVE7NEJBQ3JCO3dCQUNGO3dCQUNBNUI7b0JBQ0Y7aUJBQ0Q7WUFDSDtZQUVBLE1BQU0rQixpQkFBb0M7Z0JBQ3hDUixNQUFNdkI7Z0JBQ05rQjtnQkFDQWIsT0FBT2dCO2dCQUNQbEIsYUFBYTtZQUNmO1lBRUEsSUFBSSxDQUFDRCxPQUFPLENBQUM4QixHQUFHLENBQUNoQyxZQUFZK0I7WUFDN0JwQixRQUFRQyxHQUFHLENBQUMsQ0FBQyxXQUFXLEVBQUVaLFdBQVcsaUNBQWlDLENBQUM7UUFDekUsRUFBRSxPQUFPaUMsT0FBTztZQUNkdEIsUUFBUXNCLEtBQUssQ0FBQyxDQUFDLDJCQUEyQixFQUFFakMsV0FBVyxDQUFDLENBQUMsRUFBRWlDO1lBQzNELE1BQU1BO1FBQ1I7SUFDRjtJQUVBLE1BQU1iLFdBQVdwQixVQUFrQixFQUFpQjtRQUNsRCxNQUFNQyxTQUFTLElBQUksQ0FBQ0MsT0FBTyxDQUFDTyxHQUFHLENBQUNUO1FBQ2hDLElBQUksQ0FBQ0MsUUFBUTtZQUNYO1FBQ0Y7UUFFQSxJQUFJO1lBQ0YsSUFBSSxDQUFDQyxPQUFPLENBQUNnQyxNQUFNLENBQUNsQztZQUNwQlcsUUFBUUMsR0FBRyxDQUFDLENBQUMsV0FBVyxFQUFFWixXQUFXLFFBQVEsQ0FBQztRQUNoRCxFQUFFLE9BQU9pQyxPQUFPO1lBQ2R0QixRQUFRc0IsS0FBSyxDQUFDLENBQUMsMEJBQTBCLEVBQUVqQyxXQUFXLENBQUMsQ0FBQyxFQUFFaUM7WUFDMUQsSUFBSSxDQUFDL0IsT0FBTyxDQUFDZ0MsTUFBTSxDQUFDbEM7UUFDdEI7SUFDRjtJQUVBLE1BQU1tQyxpQkFBZ0M7UUFDcEMsTUFBTUMsZUFBZUMsTUFBTUMsSUFBSSxDQUFDLElBQUksQ0FBQ3BDLE9BQU8sQ0FBQ3FDLElBQUksSUFBSUMsR0FBRyxDQUFDeEMsQ0FBQUEsYUFDdkQsSUFBSSxDQUFDb0IsVUFBVSxDQUFDcEI7UUFHbEIsTUFBTXlDLFFBQVFDLEdBQUcsQ0FBQ047SUFDcEI7O2FBL0hPbEMsVUFBMEMsSUFBSXlDOztBQWdJdkQ7QUFFQSxxQkFBcUI7QUFDZCxNQUFNQyxZQUFZLElBQUkvQyxZQUFZIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXDIwMDIxNzkyXFxEb2N1bWVudHNcXFByb2plY3RzXFxDaGF0XFxhcHBcXGxpYlxcbWNwQ2xpZW50LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFRvb2wsIE1DUFNlcnZlckNvbmZpZyB9IGZyb20gJ0AvdHlwZXMnO1xuXG4vLyBTaW1wbGlmaWVkIE1DUCBjbGllbnQgZm9yIGRlbW8gcHVycG9zZXNcbi8vIEluIHByb2R1Y3Rpb24sIHRoaXMgd291bGQgdXNlIHRoZSBhY3R1YWwgTUNQIFNES1xuXG5leHBvcnQgaW50ZXJmYWNlIE1DUENsaWVudE1hbmFnZXIge1xuICBzZXJ2ZXJzOiBNYXA8c3RyaW5nLCBNQ1BTZXJ2ZXJJbnN0YW5jZT47XG4gIGdldEF2YWlsYWJsZVRvb2xzKCk6IFByb21pc2U8VG9vbFtdPjtcbiAgZXhlY3V0ZVRvb2xDYWxsKHNlcnZlck5hbWU6IHN0cmluZywgdG9vbE5hbWU6IHN0cmluZywgYXJnczogYW55KTogUHJvbWlzZTxhbnk+O1xuICBzdGFydFNlcnZlcihzZXJ2ZXJOYW1lOiBzdHJpbmcsIGNvbmZpZzogTUNQU2VydmVyQ29uZmlnKTogUHJvbWlzZTx2b2lkPjtcbiAgc3RvcFNlcnZlcihzZXJ2ZXJOYW1lOiBzdHJpbmcpOiBQcm9taXNlPHZvaWQ+O1xuICBzdG9wQWxsU2VydmVycygpOiBQcm9taXNlPHZvaWQ+O1xufVxuXG5pbnRlcmZhY2UgTUNQU2VydmVySW5zdGFuY2Uge1xuICBuYW1lOiBzdHJpbmc7XG4gIGNvbmZpZzogTUNQU2VydmVyQ29uZmlnO1xuICB0b29sczogVG9vbFtdO1xuICBpc0Nvbm5lY3RlZDogYm9vbGVhbjtcbn1cblxuZXhwb3J0IGNsYXNzIE1DUENsaWVudCBpbXBsZW1lbnRzIE1DUENsaWVudE1hbmFnZXIge1xuICBwdWJsaWMgc2VydmVyczogTWFwPHN0cmluZywgTUNQU2VydmVySW5zdGFuY2U+ID0gbmV3IE1hcCgpO1xuXG4gIGFzeW5jIGdldEF2YWlsYWJsZVRvb2xzKCk6IFByb21pc2U8VG9vbFtdPiB7XG4gICAgY29uc3QgYWxsVG9vbHM6IFRvb2xbXSA9IFtdO1xuXG4gICAgLy8gUmV0dXJuIGRlbW8gdG9vbHMgZm9yIG5vd1xuICAgIGZvciAoY29uc3QgW3NlcnZlck5hbWUsIHNlcnZlcl0gb2YgdGhpcy5zZXJ2ZXJzKSB7XG4gICAgICBpZiAoc2VydmVyLmlzQ29ubmVjdGVkKSB7XG4gICAgICAgIGFsbFRvb2xzLnB1c2goLi4uc2VydmVyLnRvb2xzKTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICByZXR1cm4gYWxsVG9vbHM7XG4gIH1cblxuICBhc3luYyBleGVjdXRlVG9vbENhbGwoc2VydmVyTmFtZTogc3RyaW5nLCB0b29sTmFtZTogc3RyaW5nLCBhcmdzOiBhbnkpOiBQcm9taXNlPGFueT4ge1xuICAgIGNvbnN0IHNlcnZlciA9IHRoaXMuc2VydmVycy5nZXQoc2VydmVyTmFtZSk7XG4gICAgaWYgKCFzZXJ2ZXIgfHwgIXNlcnZlci5pc0Nvbm5lY3RlZCkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGBTZXJ2ZXIgJHtzZXJ2ZXJOYW1lfSBpcyBub3QgY29ubmVjdGVkYCk7XG4gICAgfVxuXG4gICAgLy8gU2ltdWxhdGUgdG9vbCBleGVjdXRpb24gZm9yIGRlbW9cbiAgICBjb25zb2xlLmxvZyhgRXhlY3V0aW5nICR7dG9vbE5hbWV9IG9uICR7c2VydmVyTmFtZX0gd2l0aCBhcmdzOmAsIGFyZ3MpO1xuXG4gICAgLy8gUmV0dXJuIGRlbW8gcmVzcG9uc2UgYmFzZWQgb24gdG9vbFxuICAgIGlmICh0b29sTmFtZSA9PT0gJ3NlYXJjaF93ZWInKSB7XG4gICAgICByZXR1cm4ge1xuICAgICAgICBjb250ZW50OiBbe1xuICAgICAgICAgIHR5cGU6ICd0ZXh0JyxcbiAgICAgICAgICB0ZXh0OiBgU2VhcmNoIHJlc3VsdHMgZm9yIFwiJHthcmdzLnF1ZXJ5fVwiOiBUaGlzIGlzIGEgZGVtbyByZXNwb25zZSBmcm9tIHRoZSAke3NlcnZlck5hbWV9IHNlcnZlci5gXG4gICAgICAgIH1dXG4gICAgICB9O1xuICAgIH1cblxuICAgIHJldHVybiB7XG4gICAgICBjb250ZW50OiBbe1xuICAgICAgICB0eXBlOiAndGV4dCcsXG4gICAgICAgIHRleHQ6IGBUb29sICR7dG9vbE5hbWV9IGV4ZWN1dGVkIHN1Y2Nlc3NmdWxseSBvbiAke3NlcnZlck5hbWV9IHNlcnZlci5gXG4gICAgICB9XVxuICAgIH07XG4gIH1cblxuICBhc3luYyBzdGFydFNlcnZlcihzZXJ2ZXJOYW1lOiBzdHJpbmcsIGNvbmZpZzogTUNQU2VydmVyQ29uZmlnKTogUHJvbWlzZTx2b2lkPiB7XG4gICAgaWYgKHRoaXMuc2VydmVycy5oYXMoc2VydmVyTmFtZSkpIHtcbiAgICAgIGF3YWl0IHRoaXMuc3RvcFNlcnZlcihzZXJ2ZXJOYW1lKTtcbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgLy8gQ3JlYXRlIGRlbW8gdG9vbHMgYmFzZWQgb24gc2VydmVyIHR5cGVcbiAgICAgIGxldCBkZW1vVG9vbHM6IFRvb2xbXSA9IFtdO1xuXG4gICAgICBpZiAoc2VydmVyTmFtZSA9PT0gJ2NvbnRleHQ3Jykge1xuICAgICAgICBkZW1vVG9vbHMgPSBbXG4gICAgICAgICAge1xuICAgICAgICAgICAgdHlwZTogJ2Z1bmN0aW9uJyxcbiAgICAgICAgICAgIGZ1bmN0aW9uOiB7XG4gICAgICAgICAgICAgIG5hbWU6ICdzZWFyY2hfZG9jcycsXG4gICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiAnU2VhcmNoIHRocm91Z2ggZG9jdW1lbnRhdGlvbiBhbmQgY29kZSBjb250ZXh0JyxcbiAgICAgICAgICAgICAgcGFyYW1ldGVyczoge1xuICAgICAgICAgICAgICAgIHR5cGU6ICdvYmplY3QnLFxuICAgICAgICAgICAgICAgIHByb3BlcnRpZXM6IHtcbiAgICAgICAgICAgICAgICAgIHF1ZXJ5OiB7IHR5cGU6ICdzdHJpbmcnLCBkZXNjcmlwdGlvbjogJ1NlYXJjaCBxdWVyeScgfSxcbiAgICAgICAgICAgICAgICAgIGNvbnRleHQ6IHsgdHlwZTogJ3N0cmluZycsIGRlc2NyaXB0aW9uOiAnQ29udGV4dCB0eXBlIChkb2NzLCBjb2RlLCBldGMuKScgfVxuICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgcmVxdWlyZWQ6IFsncXVlcnknXVxuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgc2VydmVyTmFtZVxuICAgICAgICAgIH1cbiAgICAgICAgXTtcbiAgICAgIH0gZWxzZSBpZiAoc2VydmVyTmFtZSA9PT0gJ2V4YScpIHtcbiAgICAgICAgZGVtb1Rvb2xzID0gW1xuICAgICAgICAgIHtcbiAgICAgICAgICAgIHR5cGU6ICdmdW5jdGlvbicsXG4gICAgICAgICAgICBmdW5jdGlvbjoge1xuICAgICAgICAgICAgICBuYW1lOiAnc2VhcmNoX3dlYicsXG4gICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiAnU2VhcmNoIHRoZSB3ZWIgdXNpbmcgRXhhIEFQSScsXG4gICAgICAgICAgICAgIHBhcmFtZXRlcnM6IHtcbiAgICAgICAgICAgICAgICB0eXBlOiAnb2JqZWN0JyxcbiAgICAgICAgICAgICAgICBwcm9wZXJ0aWVzOiB7XG4gICAgICAgICAgICAgICAgICBxdWVyeTogeyB0eXBlOiAnc3RyaW5nJywgZGVzY3JpcHRpb246ICdTZWFyY2ggcXVlcnknIH0sXG4gICAgICAgICAgICAgICAgICBudW1fcmVzdWx0czogeyB0eXBlOiAnbnVtYmVyJywgZGVzY3JpcHRpb246ICdOdW1iZXIgb2YgcmVzdWx0cycsIGRlZmF1bHQ6IDEwIH1cbiAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgIHJlcXVpcmVkOiBbJ3F1ZXJ5J11cbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIHNlcnZlck5hbWVcbiAgICAgICAgICB9XG4gICAgICAgIF07XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHNlcnZlckluc3RhbmNlOiBNQ1BTZXJ2ZXJJbnN0YW5jZSA9IHtcbiAgICAgICAgbmFtZTogc2VydmVyTmFtZSxcbiAgICAgICAgY29uZmlnLFxuICAgICAgICB0b29sczogZGVtb1Rvb2xzLFxuICAgICAgICBpc0Nvbm5lY3RlZDogdHJ1ZVxuICAgICAgfTtcblxuICAgICAgdGhpcy5zZXJ2ZXJzLnNldChzZXJ2ZXJOYW1lLCBzZXJ2ZXJJbnN0YW5jZSk7XG4gICAgICBjb25zb2xlLmxvZyhgTUNQIHNlcnZlciAke3NlcnZlck5hbWV9IHN0YXJ0ZWQgc3VjY2Vzc2Z1bGx5IChkZW1vIG1vZGUpYCk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoYEZhaWxlZCB0byBzdGFydCBNQ1Agc2VydmVyICR7c2VydmVyTmFtZX06YCwgZXJyb3IpO1xuICAgICAgdGhyb3cgZXJyb3I7XG4gICAgfVxuICB9XG5cbiAgYXN5bmMgc3RvcFNlcnZlcihzZXJ2ZXJOYW1lOiBzdHJpbmcpOiBQcm9taXNlPHZvaWQ+IHtcbiAgICBjb25zdCBzZXJ2ZXIgPSB0aGlzLnNlcnZlcnMuZ2V0KHNlcnZlck5hbWUpO1xuICAgIGlmICghc2VydmVyKSB7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgdHJ5IHtcbiAgICAgIHRoaXMuc2VydmVycy5kZWxldGUoc2VydmVyTmFtZSk7XG4gICAgICBjb25zb2xlLmxvZyhgTUNQIHNlcnZlciAke3NlcnZlck5hbWV9IHN0b3BwZWRgKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcihgRXJyb3Igc3RvcHBpbmcgTUNQIHNlcnZlciAke3NlcnZlck5hbWV9OmAsIGVycm9yKTtcbiAgICAgIHRoaXMuc2VydmVycy5kZWxldGUoc2VydmVyTmFtZSk7XG4gICAgfVxuICB9XG5cbiAgYXN5bmMgc3RvcEFsbFNlcnZlcnMoKTogUHJvbWlzZTx2b2lkPiB7XG4gICAgY29uc3Qgc3RvcFByb21pc2VzID0gQXJyYXkuZnJvbSh0aGlzLnNlcnZlcnMua2V5cygpKS5tYXAoc2VydmVyTmFtZSA9PlxuICAgICAgdGhpcy5zdG9wU2VydmVyKHNlcnZlck5hbWUpXG4gICAgKTtcblxuICAgIGF3YWl0IFByb21pc2UuYWxsKHN0b3BQcm9taXNlcyk7XG4gIH1cbn1cblxuLy8gU2luZ2xldG9uIGluc3RhbmNlXG5leHBvcnQgY29uc3QgbWNwQ2xpZW50ID0gbmV3IE1DUENsaWVudCgpO1xuIl0sIm5hbWVzIjpbIk1DUENsaWVudCIsImdldEF2YWlsYWJsZVRvb2xzIiwiYWxsVG9vbHMiLCJzZXJ2ZXJOYW1lIiwic2VydmVyIiwic2VydmVycyIsImlzQ29ubmVjdGVkIiwicHVzaCIsInRvb2xzIiwiZXhlY3V0ZVRvb2xDYWxsIiwidG9vbE5hbWUiLCJhcmdzIiwiZ2V0IiwiRXJyb3IiLCJjb25zb2xlIiwibG9nIiwiY29udGVudCIsInR5cGUiLCJ0ZXh0IiwicXVlcnkiLCJzdGFydFNlcnZlciIsImNvbmZpZyIsImhhcyIsInN0b3BTZXJ2ZXIiLCJkZW1vVG9vbHMiLCJmdW5jdGlvbiIsIm5hbWUiLCJkZXNjcmlwdGlvbiIsInBhcmFtZXRlcnMiLCJwcm9wZXJ0aWVzIiwiY29udGV4dCIsInJlcXVpcmVkIiwibnVtX3Jlc3VsdHMiLCJkZWZhdWx0Iiwic2VydmVySW5zdGFuY2UiLCJzZXQiLCJlcnJvciIsImRlbGV0ZSIsInN0b3BBbGxTZXJ2ZXJzIiwic3RvcFByb21pc2VzIiwiQXJyYXkiLCJmcm9tIiwia2V5cyIsIm1hcCIsIlByb21pc2UiLCJhbGwiLCJNYXAiLCJtY3BDbGllbnQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/lib/mcpClient.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmcp%2Froute&page=%2Fapi%2Fmcp%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmcp%2Froute.ts&appDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();