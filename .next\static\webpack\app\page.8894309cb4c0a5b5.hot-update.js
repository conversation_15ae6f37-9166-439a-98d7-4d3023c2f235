"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Stack,Tooltip,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Stack,Tooltip,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/useMediaQuery/index.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Stack,Tooltip,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Stack,Tooltip,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Stack,Tooltip,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Stack,Tooltip,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Stack,Tooltip,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Tooltip/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Stack,Tooltip,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Stack,Tooltip,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Stack,Tooltip,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Stack,Tooltip,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Stack,Tooltip,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _barrel_optimize_names_Delete_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Delete,Warning!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Delete.js\");\n/* harmony import */ var _barrel_optimize_names_Delete_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Delete,Warning!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Warning.js\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_ModelSelector__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/ModelSelector */ \"(app-pages-browser)/./app/components/ModelSelector.tsx\");\n/* harmony import */ var _components_MCPServerManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/MCPServerManager */ \"(app-pages-browser)/./app/components/MCPServerManager.tsx\");\n/* harmony import */ var _components_ChatWindow_ChatWindow__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/ChatWindow/ChatWindow */ \"(app-pages-browser)/./app/components/ChatWindow/ChatWindow.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction HomePage() {\n    var _getActiveConversation, _getActiveConversation1;\n    _s();\n    const theme = (0,_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    const isMobile = (0,_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(theme.breakpoints.down('md'));\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!isMobile);\n    const [mcpConfig, setMcpConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        mcpServers: {}\n    });\n    const [availableTools, setAvailableTools] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [conversations, setConversations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [activeConversationId, setActiveConversationId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [modelCapabilities, setModelCapabilities] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deleteDialogOpen, setDeleteDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            loadMCPConfig();\n            loadConversations();\n        }\n    }[\"HomePage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            setSidebarOpen(!isMobile);\n        }\n    }[\"HomePage.useEffect\"], [\n        isMobile\n    ]);\n    const loadMCPConfig = async ()=>{\n        try {\n            const response = await fetch('/api/mcp');\n            if (response.ok) {\n                const data = await response.json();\n                console.log('Loaded MCP data:', data);\n                console.log('Available tools:', data.tools);\n                setMcpConfig({\n                    mcpServers: data.mcpServers || {}\n                });\n                setAvailableTools(data.tools || []);\n                console.log('Set available tools state:', data.tools || []);\n            }\n        } catch (error) {\n            console.error('Error loading MCP config:', error);\n        }\n    };\n    const loadConversations = ()=>{\n        // Load conversations from localStorage\n        const saved = localStorage.getItem('ollama-chat-conversations');\n        if (saved) {\n            try {\n                const parsed = JSON.parse(saved);\n                setConversations(parsed.map((conv)=>({\n                        ...conv,\n                        timestamp: new Date(conv.timestamp),\n                        messages: conv.messages.map((msg)=>({\n                                ...msg,\n                                timestamp: new Date(msg.timestamp)\n                            }))\n                    })));\n            } catch (error) {\n                console.error('Error loading conversations:', error);\n            }\n        }\n    };\n    const saveConversations = (convs)=>{\n        localStorage.setItem('ollama-chat-conversations', JSON.stringify(convs));\n    };\n    const createNewConversation = ()=>{\n        const newConversation = {\n            id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n            title: 'New Conversation',\n            lastMessage: '',\n            timestamp: new Date(),\n            messageCount: 0,\n            messages: []\n        };\n        const updatedConversations = [\n            newConversation,\n            ...conversations\n        ];\n        setConversations(updatedConversations);\n        setActiveConversationId(newConversation.id);\n        saveConversations(updatedConversations);\n        if (isMobile) {\n            setSidebarOpen(false);\n        }\n    };\n    const selectConversation = (id)=>{\n        setActiveConversationId(id);\n        if (isMobile) {\n            setSidebarOpen(false);\n        }\n    };\n    const deleteConversation = (id)=>{\n        const updatedConversations = conversations.filter((conv)=>conv.id !== id);\n        setConversations(updatedConversations);\n        saveConversations(updatedConversations);\n        if (activeConversationId === id) {\n            setActiveConversationId(updatedConversations.length > 0 ? updatedConversations[0].id : null);\n        }\n    };\n    const handleDeleteCurrentChat = ()=>{\n        if (activeConversationId) {\n            deleteConversation(activeConversationId);\n            setDeleteDialogOpen(false);\n        }\n    };\n    const updateConversationWithMessage = (message)=>{\n        const activeConv = conversations.find((conv)=>conv.id === activeConversationId);\n        if (!activeConv) return;\n        const updatedConversation = {\n            ...activeConv,\n            messages: [\n                ...activeConv.messages,\n                message\n            ],\n            lastMessage: message.content,\n            timestamp: new Date(),\n            messageCount: activeConv.messageCount + 1,\n            title: activeConv.title === 'New Conversation' && message.role === 'user' ? message.content.substring(0, 50) + (message.content.length > 50 ? '...' : '') : activeConv.title\n        };\n        const updatedConversations = conversations.map((conv)=>conv.id === activeConversationId ? updatedConversation : conv);\n        setConversations(updatedConversations);\n        saveConversations(updatedConversations);\n    };\n    const getActiveConversation = ()=>{\n        return conversations.find((conv)=>conv.id === activeConversationId) || null;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n        initial: {\n            opacity: 0\n        },\n        animate: {\n            opacity: 1\n        },\n        transition: {\n            duration: 0.5\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            sx: {\n                height: '100vh',\n                display: 'flex',\n                overflow: 'hidden'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    sx: {\n                        width: sidebarOpen ? 320 : 0,\n                        transition: 'width 0.3s ease',\n                        overflow: 'hidden',\n                        borderRight: sidebarOpen ? '1px solid rgba(255, 255, 255, 0.1)' : 'none'\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        sx: {\n                            width: 320,\n                            height: '100%',\n                            display: 'flex',\n                            flexDirection: 'column',\n                            backdropFilter: 'blur(20px)',\n                            background: 'rgba(255, 255, 255, 0.03)'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                sx: {\n                                    p: 3,\n                                    borderBottom: '1px solid rgba(255, 255, 255, 0.1)'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        variant: \"h6\",\n                                        sx: {\n                                            mb: 2,\n                                            fontWeight: 600\n                                        },\n                                        children: \"Ollama Chat\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ModelSelector__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        selectedModel: selectedModel,\n                                        onModelSelect: setSelectedModel,\n                                        onModelCapabilitiesChange: setModelCapabilities\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                sx: {\n                                    flex: 1,\n                                    overflow: 'auto',\n                                    p: 2\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        sx: {\n                                            mb: 2\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            fullWidth: true,\n                                            variant: \"outlined\",\n                                            onClick: createNewConversation,\n                                            sx: {\n                                                mb: 2\n                                            },\n                                            children: \"New Chat\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 13\n                                    }, this),\n                                    conversations.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        variant: \"body2\",\n                                        color: \"text.secondary\",\n                                        sx: {\n                                            textAlign: 'center',\n                                            py: 4\n                                        },\n                                        children: \"No conversations yet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 15\n                                    }, this) : conversations.map((conversation)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            onClick: ()=>selectConversation(conversation.id),\n                                            sx: {\n                                                p: 2,\n                                                mb: 1,\n                                                borderRadius: 2,\n                                                cursor: 'pointer',\n                                                background: conversation.id === activeConversationId ? 'rgba(255, 255, 255, 0.1)' : 'transparent',\n                                                border: conversation.id === activeConversationId ? '1px solid rgba(255, 255, 255, 0.2)' : '1px solid transparent',\n                                                transition: 'all 0.2s ease',\n                                                '&:hover': {\n                                                    background: 'rgba(255, 255, 255, 0.05)'\n                                                }\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    variant: \"body2\",\n                                                    fontWeight: 500,\n                                                    noWrap: true,\n                                                    children: conversation.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    variant: \"caption\",\n                                                    color: \"text.secondary\",\n                                                    noWrap: true,\n                                                    children: [\n                                                        conversation.messageCount,\n                                                        \" messages\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, conversation.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 17\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                sx: {\n                                    p: 2,\n                                    borderTop: '1px solid rgba(255, 255, 255, 0.1)'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    spacing: 1,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MCPServerManager__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        config: mcpConfig,\n                                        onConfigUpdate: setMcpConfig,\n                                        availableTools: availableTools\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                    lineNumber: 183,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    sx: {\n                        flex: 1,\n                        display: 'flex',\n                        flexDirection: 'column'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            sx: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                p: 2,\n                                borderBottom: '1px solid rgba(255, 255, 255, 0.1)',\n                                backdropFilter: 'blur(20px)',\n                                background: 'rgba(255, 255, 255, 0.03)'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    onClick: ()=>setSidebarOpen(!sidebarOpen),\n                                    sx: {\n                                        mr: 2,\n                                        minWidth: 'auto',\n                                        p: 1\n                                    },\n                                    children: \"☰\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    variant: \"h6\",\n                                    sx: {\n                                        flex: 1\n                                    },\n                                    children: ((_getActiveConversation = getActiveConversation()) === null || _getActiveConversation === void 0 ? void 0 : _getActiveConversation.title) || 'Select a conversation'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 11\n                                }, this),\n                                activeConversationId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    title: \"Delete current chat\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        onClick: ()=>setDeleteDialogOpen(true),\n                                        sx: {\n                                            color: 'rgba(255, 255, 255, 0.7)',\n                                            '&:hover': {\n                                                color: '#ff4444',\n                                                backgroundColor: 'rgba(255, 68, 68, 0.1)'\n                                            },\n                                            mr: 1\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Delete_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 13\n                                }, this),\n                                selectedModel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    sx: {\n                                        display: 'flex',\n                                        gap: 1\n                                    },\n                                    children: [\n                                        (modelCapabilities === null || modelCapabilities === void 0 ? void 0 : modelCapabilities.supportsTools) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            sx: {\n                                                px: 1,\n                                                py: 0.5,\n                                                borderRadius: 1,\n                                                background: 'rgba(255, 255, 255, 0.1)',\n                                                fontSize: '0.75rem'\n                                            },\n                                            children: \"Tools\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 17\n                                        }, this),\n                                        (modelCapabilities === null || modelCapabilities === void 0 ? void 0 : modelCapabilities.supportsVision) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            sx: {\n                                                px: 1,\n                                                py: 0.5,\n                                                borderRadius: 1,\n                                                background: 'rgba(255, 255, 255, 0.1)',\n                                                fontSize: '0.75rem'\n                                            },\n                                            children: \"Vision\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                            lineNumber: 336,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            sx: {\n                                flex: 1,\n                                overflow: 'hidden'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatWindow_ChatWindow__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                selectedModel: selectedModel,\n                                availableTools: availableTools,\n                                conversation: getActiveConversation(),\n                                onMessageSent: updateConversationWithMessage,\n                                modelCapabilities: modelCapabilities\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                    lineNumber: 280,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                    open: deleteDialogOpen,\n                    onClose: ()=>setDeleteDialogOpen(false),\n                    slotProps: {\n                        paper: {\n                            sx: {\n                                backdropFilter: 'blur(20px)',\n                                background: 'rgba(255, 255, 255, 0.1)',\n                                border: '1px solid rgba(255, 255, 255, 0.2)',\n                                color: 'white'\n                            }\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            sx: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                color: 'white'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Delete_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    sx: {\n                                        mr: 1,\n                                        color: '#ff4444'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                    lineNumber: 378,\n                                    columnNumber: 11\n                                }, this),\n                                \"Delete Chat\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                            lineNumber: 377,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                color: \"rgba(255, 255, 255, 0.8)\",\n                                children: [\n                                    'Are you sure you want to delete \"',\n                                    (_getActiveConversation1 = getActiveConversation()) === null || _getActiveConversation1 === void 0 ? void 0 : _getActiveConversation1.title,\n                                    '\"? This action cannot be undone and all messages in this conversation will be permanently lost.'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                            lineNumber: 381,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    onClick: ()=>setDeleteDialogOpen(false),\n                                    sx: {\n                                        color: 'rgba(255, 255, 255, 0.7)'\n                                    },\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    onClick: handleDeleteCurrentChat,\n                                    variant: \"contained\",\n                                    sx: {\n                                        bgcolor: '#ff4444',\n                                        '&:hover': {\n                                            bgcolor: '#ff3333'\n                                        }\n                                    },\n                                    children: \"Delete\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                    lineNumber: 394,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                            lineNumber: 387,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                    lineNumber: 363,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n            lineNumber: 181,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n        lineNumber: 176,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"hMCcnxoj+5ssoIMUHv4IVSGjiK0=\", false, function() {\n    return [\n        _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Stack_Tooltip_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    ];\n});\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});