'use client';

import React from 'react';
import {
  A<PERSON>B<PERSON>,
  Too<PERSON>bar,
  Typography,
  IconButton,
  Button,
  Box,
  Chip,
  Tooltip,
  useTheme,
  useMediaQuery,
  Avatar,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Settings as SettingsIcon,
  Refresh as RefreshIcon,
  SmartToy as BotIcon,
  Circle as CircleIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';

interface ModernAppBarProps {
  onMenuClick: () => void;
  onSettingsClick: () => void;
  onRefreshClick: () => void;
  selectedModel: string | null;
  serverCount: number;
  toolCount: number;
  isConnected: boolean;
}

export default function ModernAppBar({
  onMenuClick,
  onSettingsClick,
  onRefreshClick,
  selectedModel,
  serverCount,
  toolCount,
  isConnected,
}: ModernAppBarProps) {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  return (
    <AppBar
      position="static"
      elevation={0}
      sx={{
        bgcolor: 'background.paper',
        borderBottom: 1,
        borderColor: 'divider',
        color: 'text.primary',
      }}
    >
      <Toolbar sx={{ px: { xs: 2, sm: 3 } }}>
        {/* Menu Button */}
        <motion.div
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <IconButton
            edge="start"
            onClick={onMenuClick}
            sx={{
              mr: 2,
              color: 'rgba(255, 255, 255, 0.9)',
              transition: 'all 0.3s ease',
              '&:hover': {
                bgcolor: 'rgba(255, 255, 255, 0.1)',
                color: 'white',
                transform: 'translateY(-1px)',
              },
            }}
          >
            <MenuIcon />
          </IconButton>
        </motion.div>

        {/* Logo and Title */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', mr: 3 }}>
            <motion.div
              whileHover={{ rotate: 360 }}
              transition={{ duration: 0.6 }}
            >
              <Avatar
                sx={{
                  width: 32,
                  height: 32,
                  background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1))',
                  border: '1px solid rgba(255, 255, 255, 0.2)',
                  mr: 1.5,
                  color: 'white',
                }}
              >
                <BotIcon fontSize="small" />
              </Avatar>
            </motion.div>
            <Box>
              <Typography
                variant="h6"
                component="div"
                sx={{
                  fontWeight: 600,
                  fontSize: '1.1rem',
                  lineHeight: 1.2,
                  color: 'white',
                  background: 'linear-gradient(135deg, #ffffff, #f0f0f0)',
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                }}
              >
                Ollama MCP Chat
              </Typography>
              {!isMobile && (
                <Typography
                  variant="caption"
                  sx={{
                    fontSize: '0.75rem',
                    color: 'rgba(255, 255, 255, 0.7)',
                  }}
                >
                  AI Assistant with Tool Integration
                </Typography>
              )}
            </Box>
          </Box>
        </motion.div>

        {/* Spacer */}
        <Box sx={{ flexGrow: 1 }} />

        {/* Status Indicators */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {/* Connection Status */}
          <Tooltip title={isConnected ? 'Connected to Ollama' : 'Disconnected from Ollama'}>
            <Chip
              icon={
                <CircleIcon
                  sx={{
                    fontSize: '0.75rem !important',
                    color: isConnected ? 'success.main' : 'error.main',
                  }}
                />
              }
              label={isConnected ? 'Online' : 'Offline'}
              size="small"
              variant="outlined"
              sx={{
                height: 24,
                fontSize: '0.75rem',
                borderColor: isConnected ? 'success.main' : 'error.main',
                color: isConnected ? 'success.main' : 'error.main',
                display: { xs: 'none', sm: 'flex' },
              }}
            />
          </Tooltip>

          {/* Model Status */}
          {selectedModel && (
            <Tooltip title={`Active model: ${selectedModel}`}>
              <Chip
                label={isMobile ? selectedModel.split(':')[0] : selectedModel}
                size="small"
                color="primary"
                variant="outlined"
                sx={{
                  height: 24,
                  fontSize: '0.75rem',
                  maxWidth: { xs: 100, sm: 200 },
                }}
              />
            </Tooltip>
          )}

          {/* MCP Status */}
          {!isMobile && (
            <Tooltip
              title={`${serverCount} MCP server(s) configured, ${toolCount} tool(s) available`}
            >
              <Chip
                label={`${serverCount} servers, ${toolCount} tools`}
                size="small"
                color={serverCount > 0 ? 'success' : 'default'}
                variant="outlined"
                sx={{
                  height: 24,
                  fontSize: '0.75rem',
                }}
              />
            </Tooltip>
          )}
        </Box>

        {/* Action Buttons */}
        <Box sx={{ display: 'flex', alignItems: 'center', ml: 2, gap: 1 }}>
          <Tooltip title="Refresh MCP configuration">
            <motion.div
              whileHover={{ scale: 1.1, rotate: 180 }}
              whileTap={{ scale: 0.9 }}
            >
              <IconButton
                onClick={onRefreshClick}
                size="small"
                sx={{
                  color: 'rgba(255, 255, 255, 0.7)',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    bgcolor: 'rgba(255, 255, 255, 0.1)',
                    color: 'white',
                    transform: 'translateY(-1px)',
                  },
                }}
              >
                <RefreshIcon fontSize="small" />
              </IconButton>
            </motion.div>
          </Tooltip>

          <Tooltip title="Configure MCP servers">
            <motion.div
              whileHover={{ scale: 1.1, rotate: 90 }}
              whileTap={{ scale: 0.9 }}
            >
              <IconButton
                onClick={onSettingsClick}
                size="small"
                sx={{
                  color: 'rgba(255, 255, 255, 0.7)',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    bgcolor: 'rgba(255, 255, 255, 0.1)',
                    color: 'white',
                    transform: 'translateY(-1px)',
                  },
                }}
              >
                <SettingsIcon fontSize="small" />
              </IconButton>
            </motion.div>
          </Tooltip>
        </Box>
      </Toolbar>
    </AppBar>
  );
}
