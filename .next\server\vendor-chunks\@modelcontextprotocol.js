"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@modelcontextprotocol";
exports.ids = ["vendor-chunks/@modelcontextprotocol"];
exports.modules = {

/***/ "(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/client/index.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@modelcontextprotocol/sdk/dist/esm/client/index.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Client: () => (/* binding */ Client)\n/* harmony export */ });\n/* harmony import */ var _shared_protocol_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../shared/protocol.js */ \"(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/shared/protocol.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../types.js */ \"(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/types.js\");\n/* harmony import */ var ajv__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ajv */ \"(rsc)/./node_modules/ajv/lib/ajv.js\");\n\n\n\n/**\n * An MCP client on top of a pluggable transport.\n *\n * The client will automatically begin the initialization flow with the server when connect() is called.\n *\n * To use with custom types, extend the base Request/Notification/Result types and pass them as type parameters:\n *\n * ```typescript\n * // Custom schemas\n * const CustomRequestSchema = RequestSchema.extend({...})\n * const CustomNotificationSchema = NotificationSchema.extend({...})\n * const CustomResultSchema = ResultSchema.extend({...})\n *\n * // Type aliases\n * type CustomRequest = z.infer<typeof CustomRequestSchema>\n * type CustomNotification = z.infer<typeof CustomNotificationSchema>\n * type CustomResult = z.infer<typeof CustomResultSchema>\n *\n * // Create typed client\n * const client = new Client<CustomRequest, CustomNotification, CustomResult>({\n *   name: \"CustomClient\",\n *   version: \"1.0.0\"\n * })\n * ```\n */\nclass Client extends _shared_protocol_js__WEBPACK_IMPORTED_MODULE_0__.Protocol {\n    /**\n     * Initializes this client with the given name and version information.\n     */\n    constructor(_clientInfo, options) {\n        var _a;\n        super(options);\n        this._clientInfo = _clientInfo;\n        this._cachedToolOutputValidators = new Map();\n        this._capabilities = (_a = options === null || options === void 0 ? void 0 : options.capabilities) !== null && _a !== void 0 ? _a : {};\n        this._ajv = new ajv__WEBPACK_IMPORTED_MODULE_2__();\n    }\n    /**\n     * Registers new capabilities. This can only be called before connecting to a transport.\n     *\n     * The new capabilities will be merged with any existing capabilities previously given (e.g., at initialization).\n     */\n    registerCapabilities(capabilities) {\n        if (this.transport) {\n            throw new Error(\"Cannot register capabilities after connecting to transport\");\n        }\n        this._capabilities = (0,_shared_protocol_js__WEBPACK_IMPORTED_MODULE_0__.mergeCapabilities)(this._capabilities, capabilities);\n    }\n    assertCapability(capability, method) {\n        var _a;\n        if (!((_a = this._serverCapabilities) === null || _a === void 0 ? void 0 : _a[capability])) {\n            throw new Error(`Server does not support ${capability} (required for ${method})`);\n        }\n    }\n    async connect(transport, options) {\n        await super.connect(transport);\n        // When transport sessionId is already set this means we are trying to reconnect.\n        // In this case we don't need to initialize again.\n        if (transport.sessionId !== undefined) {\n            return;\n        }\n        try {\n            const result = await this.request({\n                method: \"initialize\",\n                params: {\n                    protocolVersion: _types_js__WEBPACK_IMPORTED_MODULE_1__.LATEST_PROTOCOL_VERSION,\n                    capabilities: this._capabilities,\n                    clientInfo: this._clientInfo,\n                },\n            }, _types_js__WEBPACK_IMPORTED_MODULE_1__.InitializeResultSchema, options);\n            if (result === undefined) {\n                throw new Error(`Server sent invalid initialize result: ${result}`);\n            }\n            if (!_types_js__WEBPACK_IMPORTED_MODULE_1__.SUPPORTED_PROTOCOL_VERSIONS.includes(result.protocolVersion)) {\n                throw new Error(`Server's protocol version is not supported: ${result.protocolVersion}`);\n            }\n            this._serverCapabilities = result.capabilities;\n            this._serverVersion = result.serverInfo;\n            // HTTP transports must set the protocol version in each header after initialization.\n            if (transport.setProtocolVersion) {\n                transport.setProtocolVersion(result.protocolVersion);\n            }\n            this._instructions = result.instructions;\n            await this.notification({\n                method: \"notifications/initialized\",\n            });\n        }\n        catch (error) {\n            // Disconnect if initialization fails.\n            void this.close();\n            throw error;\n        }\n    }\n    /**\n     * After initialization has completed, this will be populated with the server's reported capabilities.\n     */\n    getServerCapabilities() {\n        return this._serverCapabilities;\n    }\n    /**\n     * After initialization has completed, this will be populated with information about the server's name and version.\n     */\n    getServerVersion() {\n        return this._serverVersion;\n    }\n    /**\n     * After initialization has completed, this may be populated with information about the server's instructions.\n     */\n    getInstructions() {\n        return this._instructions;\n    }\n    assertCapabilityForMethod(method) {\n        var _a, _b, _c, _d, _e;\n        switch (method) {\n            case \"logging/setLevel\":\n                if (!((_a = this._serverCapabilities) === null || _a === void 0 ? void 0 : _a.logging)) {\n                    throw new Error(`Server does not support logging (required for ${method})`);\n                }\n                break;\n            case \"prompts/get\":\n            case \"prompts/list\":\n                if (!((_b = this._serverCapabilities) === null || _b === void 0 ? void 0 : _b.prompts)) {\n                    throw new Error(`Server does not support prompts (required for ${method})`);\n                }\n                break;\n            case \"resources/list\":\n            case \"resources/templates/list\":\n            case \"resources/read\":\n            case \"resources/subscribe\":\n            case \"resources/unsubscribe\":\n                if (!((_c = this._serverCapabilities) === null || _c === void 0 ? void 0 : _c.resources)) {\n                    throw new Error(`Server does not support resources (required for ${method})`);\n                }\n                if (method === \"resources/subscribe\" &&\n                    !this._serverCapabilities.resources.subscribe) {\n                    throw new Error(`Server does not support resource subscriptions (required for ${method})`);\n                }\n                break;\n            case \"tools/call\":\n            case \"tools/list\":\n                if (!((_d = this._serverCapabilities) === null || _d === void 0 ? void 0 : _d.tools)) {\n                    throw new Error(`Server does not support tools (required for ${method})`);\n                }\n                break;\n            case \"completion/complete\":\n                if (!((_e = this._serverCapabilities) === null || _e === void 0 ? void 0 : _e.completions)) {\n                    throw new Error(`Server does not support completions (required for ${method})`);\n                }\n                break;\n            case \"initialize\":\n                // No specific capability required for initialize\n                break;\n            case \"ping\":\n                // No specific capability required for ping\n                break;\n        }\n    }\n    assertNotificationCapability(method) {\n        var _a;\n        switch (method) {\n            case \"notifications/roots/list_changed\":\n                if (!((_a = this._capabilities.roots) === null || _a === void 0 ? void 0 : _a.listChanged)) {\n                    throw new Error(`Client does not support roots list changed notifications (required for ${method})`);\n                }\n                break;\n            case \"notifications/initialized\":\n                // No specific capability required for initialized\n                break;\n            case \"notifications/cancelled\":\n                // Cancellation notifications are always allowed\n                break;\n            case \"notifications/progress\":\n                // Progress notifications are always allowed\n                break;\n        }\n    }\n    assertRequestHandlerCapability(method) {\n        switch (method) {\n            case \"sampling/createMessage\":\n                if (!this._capabilities.sampling) {\n                    throw new Error(`Client does not support sampling capability (required for ${method})`);\n                }\n                break;\n            case \"elicitation/create\":\n                if (!this._capabilities.elicitation) {\n                    throw new Error(`Client does not support elicitation capability (required for ${method})`);\n                }\n                break;\n            case \"roots/list\":\n                if (!this._capabilities.roots) {\n                    throw new Error(`Client does not support roots capability (required for ${method})`);\n                }\n                break;\n            case \"ping\":\n                // No specific capability required for ping\n                break;\n        }\n    }\n    async ping(options) {\n        return this.request({ method: \"ping\" }, _types_js__WEBPACK_IMPORTED_MODULE_1__.EmptyResultSchema, options);\n    }\n    async complete(params, options) {\n        return this.request({ method: \"completion/complete\", params }, _types_js__WEBPACK_IMPORTED_MODULE_1__.CompleteResultSchema, options);\n    }\n    async setLoggingLevel(level, options) {\n        return this.request({ method: \"logging/setLevel\", params: { level } }, _types_js__WEBPACK_IMPORTED_MODULE_1__.EmptyResultSchema, options);\n    }\n    async getPrompt(params, options) {\n        return this.request({ method: \"prompts/get\", params }, _types_js__WEBPACK_IMPORTED_MODULE_1__.GetPromptResultSchema, options);\n    }\n    async listPrompts(params, options) {\n        return this.request({ method: \"prompts/list\", params }, _types_js__WEBPACK_IMPORTED_MODULE_1__.ListPromptsResultSchema, options);\n    }\n    async listResources(params, options) {\n        return this.request({ method: \"resources/list\", params }, _types_js__WEBPACK_IMPORTED_MODULE_1__.ListResourcesResultSchema, options);\n    }\n    async listResourceTemplates(params, options) {\n        return this.request({ method: \"resources/templates/list\", params }, _types_js__WEBPACK_IMPORTED_MODULE_1__.ListResourceTemplatesResultSchema, options);\n    }\n    async readResource(params, options) {\n        return this.request({ method: \"resources/read\", params }, _types_js__WEBPACK_IMPORTED_MODULE_1__.ReadResourceResultSchema, options);\n    }\n    async subscribeResource(params, options) {\n        return this.request({ method: \"resources/subscribe\", params }, _types_js__WEBPACK_IMPORTED_MODULE_1__.EmptyResultSchema, options);\n    }\n    async unsubscribeResource(params, options) {\n        return this.request({ method: \"resources/unsubscribe\", params }, _types_js__WEBPACK_IMPORTED_MODULE_1__.EmptyResultSchema, options);\n    }\n    async callTool(params, resultSchema = _types_js__WEBPACK_IMPORTED_MODULE_1__.CallToolResultSchema, options) {\n        const result = await this.request({ method: \"tools/call\", params }, resultSchema, options);\n        // Check if the tool has an outputSchema\n        const validator = this.getToolOutputValidator(params.name);\n        if (validator) {\n            // If tool has outputSchema, it MUST return structuredContent (unless it's an error)\n            if (!result.structuredContent && !result.isError) {\n                throw new _types_js__WEBPACK_IMPORTED_MODULE_1__.McpError(_types_js__WEBPACK_IMPORTED_MODULE_1__.ErrorCode.InvalidRequest, `Tool ${params.name} has an output schema but did not return structured content`);\n            }\n            // Only validate structured content if present (not when there's an error)\n            if (result.structuredContent) {\n                try {\n                    // Validate the structured content (which is already an object) against the schema\n                    const isValid = validator(result.structuredContent);\n                    if (!isValid) {\n                        throw new _types_js__WEBPACK_IMPORTED_MODULE_1__.McpError(_types_js__WEBPACK_IMPORTED_MODULE_1__.ErrorCode.InvalidParams, `Structured content does not match the tool's output schema: ${this._ajv.errorsText(validator.errors)}`);\n                    }\n                }\n                catch (error) {\n                    if (error instanceof _types_js__WEBPACK_IMPORTED_MODULE_1__.McpError) {\n                        throw error;\n                    }\n                    throw new _types_js__WEBPACK_IMPORTED_MODULE_1__.McpError(_types_js__WEBPACK_IMPORTED_MODULE_1__.ErrorCode.InvalidParams, `Failed to validate structured content: ${error instanceof Error ? error.message : String(error)}`);\n                }\n            }\n        }\n        return result;\n    }\n    cacheToolOutputSchemas(tools) {\n        this._cachedToolOutputValidators.clear();\n        for (const tool of tools) {\n            // If the tool has an outputSchema, create and cache the Ajv validator\n            if (tool.outputSchema) {\n                try {\n                    const validator = this._ajv.compile(tool.outputSchema);\n                    this._cachedToolOutputValidators.set(tool.name, validator);\n                }\n                catch (_a) {\n                    // Ignore schema compilation errors\n                }\n            }\n        }\n    }\n    getToolOutputValidator(toolName) {\n        return this._cachedToolOutputValidators.get(toolName);\n    }\n    async listTools(params, options) {\n        const result = await this.request({ method: \"tools/list\", params }, _types_js__WEBPACK_IMPORTED_MODULE_1__.ListToolsResultSchema, options);\n        // Cache the tools and their output schemas for future validation\n        this.cacheToolOutputSchemas(result.tools);\n        return result;\n    }\n    async sendRootsListChanged() {\n        return this.notification({ method: \"notifications/roots/list_changed\" });\n    }\n}\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/client/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/client/stdio.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@modelcontextprotocol/sdk/dist/esm/client/stdio.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_INHERITED_ENV_VARS: () => (/* binding */ DEFAULT_INHERITED_ENV_VARS),\n/* harmony export */   StdioClientTransport: () => (/* binding */ StdioClientTransport),\n/* harmony export */   getDefaultEnvironment: () => (/* binding */ getDefaultEnvironment)\n/* harmony export */ });\n/* harmony import */ var cross_spawn__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! cross-spawn */ \"(rsc)/./node_modules/cross-spawn/index.js\");\n/* harmony import */ var node_process__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! node:process */ \"node:process\");\n/* harmony import */ var node_stream__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! node:stream */ \"node:stream\");\n/* harmony import */ var _shared_stdio_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../shared/stdio.js */ \"(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/shared/stdio.js\");\n\n\n\n\n/**\n * Environment variables to inherit by default, if an environment is not explicitly given.\n */\nconst DEFAULT_INHERITED_ENV_VARS = node_process__WEBPACK_IMPORTED_MODULE_1__.platform === \"win32\"\n    ? [\n        \"APPDATA\",\n        \"HOMEDRIVE\",\n        \"HOMEPATH\",\n        \"LOCALAPPDATA\",\n        \"PATH\",\n        \"PROCESSOR_ARCHITECTURE\",\n        \"SYSTEMDRIVE\",\n        \"SYSTEMROOT\",\n        \"TEMP\",\n        \"USERNAME\",\n        \"USERPROFILE\",\n        \"PROGRAMFILES\",\n    ]\n    : /* list inspired by the default env inheritance of sudo */\n        [\"HOME\", \"LOGNAME\", \"PATH\", \"SHELL\", \"TERM\", \"USER\"];\n/**\n * Returns a default environment object including only environment variables deemed safe to inherit.\n */\nfunction getDefaultEnvironment() {\n    const env = {};\n    for (const key of DEFAULT_INHERITED_ENV_VARS) {\n        const value = node_process__WEBPACK_IMPORTED_MODULE_1__.env[key];\n        if (value === undefined) {\n            continue;\n        }\n        if (value.startsWith(\"()\")) {\n            // Skip functions, which are a security risk.\n            continue;\n        }\n        env[key] = value;\n    }\n    return env;\n}\n/**\n * Client transport for stdio: this will connect to a server by spawning a process and communicating with it over stdin/stdout.\n *\n * This transport is only available in Node.js environments.\n */\nclass StdioClientTransport {\n    constructor(server) {\n        this._abortController = new AbortController();\n        this._readBuffer = new _shared_stdio_js__WEBPACK_IMPORTED_MODULE_3__.ReadBuffer();\n        this._stderrStream = null;\n        this._serverParams = server;\n        if (server.stderr === \"pipe\" || server.stderr === \"overlapped\") {\n            this._stderrStream = new node_stream__WEBPACK_IMPORTED_MODULE_2__.PassThrough();\n        }\n    }\n    /**\n     * Starts the server process and prepares to communicate with it.\n     */\n    async start() {\n        if (this._process) {\n            throw new Error(\"StdioClientTransport already started! If using Client class, note that connect() calls start() automatically.\");\n        }\n        return new Promise((resolve, reject) => {\n            var _a, _b, _c, _d, _e;\n            this._process = cross_spawn__WEBPACK_IMPORTED_MODULE_0__(this._serverParams.command, (_a = this._serverParams.args) !== null && _a !== void 0 ? _a : [], {\n                // merge default env with server env because mcp server needs some env vars\n                env: {\n                    ...getDefaultEnvironment(),\n                    ...this._serverParams.env,\n                },\n                stdio: [\"pipe\", \"pipe\", (_b = this._serverParams.stderr) !== null && _b !== void 0 ? _b : \"inherit\"],\n                shell: false,\n                signal: this._abortController.signal,\n                windowsHide: node_process__WEBPACK_IMPORTED_MODULE_1__.platform === \"win32\" && isElectron(),\n                cwd: this._serverParams.cwd,\n            });\n            this._process.on(\"error\", (error) => {\n                var _a, _b;\n                if (error.name === \"AbortError\") {\n                    // Expected when close() is called.\n                    (_a = this.onclose) === null || _a === void 0 ? void 0 : _a.call(this);\n                    return;\n                }\n                reject(error);\n                (_b = this.onerror) === null || _b === void 0 ? void 0 : _b.call(this, error);\n            });\n            this._process.on(\"spawn\", () => {\n                resolve();\n            });\n            this._process.on(\"close\", (_code) => {\n                var _a;\n                this._process = undefined;\n                (_a = this.onclose) === null || _a === void 0 ? void 0 : _a.call(this);\n            });\n            (_c = this._process.stdin) === null || _c === void 0 ? void 0 : _c.on(\"error\", (error) => {\n                var _a;\n                (_a = this.onerror) === null || _a === void 0 ? void 0 : _a.call(this, error);\n            });\n            (_d = this._process.stdout) === null || _d === void 0 ? void 0 : _d.on(\"data\", (chunk) => {\n                this._readBuffer.append(chunk);\n                this.processReadBuffer();\n            });\n            (_e = this._process.stdout) === null || _e === void 0 ? void 0 : _e.on(\"error\", (error) => {\n                var _a;\n                (_a = this.onerror) === null || _a === void 0 ? void 0 : _a.call(this, error);\n            });\n            if (this._stderrStream && this._process.stderr) {\n                this._process.stderr.pipe(this._stderrStream);\n            }\n        });\n    }\n    /**\n     * The stderr stream of the child process, if `StdioServerParameters.stderr` was set to \"pipe\" or \"overlapped\".\n     *\n     * If stderr piping was requested, a PassThrough stream is returned _immediately_, allowing callers to\n     * attach listeners before the start method is invoked. This prevents loss of any early\n     * error output emitted by the child process.\n     */\n    get stderr() {\n        var _a, _b;\n        if (this._stderrStream) {\n            return this._stderrStream;\n        }\n        return (_b = (_a = this._process) === null || _a === void 0 ? void 0 : _a.stderr) !== null && _b !== void 0 ? _b : null;\n    }\n    /**\n     * The child process pid spawned by this transport.\n     *\n     * This is only available after the transport has been started.\n     */\n    get pid() {\n        var _a, _b;\n        return (_b = (_a = this._process) === null || _a === void 0 ? void 0 : _a.pid) !== null && _b !== void 0 ? _b : null;\n    }\n    processReadBuffer() {\n        var _a, _b;\n        while (true) {\n            try {\n                const message = this._readBuffer.readMessage();\n                if (message === null) {\n                    break;\n                }\n                (_a = this.onmessage) === null || _a === void 0 ? void 0 : _a.call(this, message);\n            }\n            catch (error) {\n                (_b = this.onerror) === null || _b === void 0 ? void 0 : _b.call(this, error);\n            }\n        }\n    }\n    async close() {\n        this._abortController.abort();\n        this._process = undefined;\n        this._readBuffer.clear();\n    }\n    send(message) {\n        return new Promise((resolve) => {\n            var _a;\n            if (!((_a = this._process) === null || _a === void 0 ? void 0 : _a.stdin)) {\n                throw new Error(\"Not connected\");\n            }\n            const json = (0,_shared_stdio_js__WEBPACK_IMPORTED_MODULE_3__.serializeMessage)(message);\n            if (this._process.stdin.write(json)) {\n                resolve();\n            }\n            else {\n                this._process.stdin.once(\"drain\", resolve);\n            }\n        });\n    }\n}\nfunction isElectron() {\n    return \"type\" in node_process__WEBPACK_IMPORTED_MODULE_1__;\n}\n//# sourceMappingURL=stdio.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/client/stdio.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/shared/protocol.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@modelcontextprotocol/sdk/dist/esm/shared/protocol.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_REQUEST_TIMEOUT_MSEC: () => (/* binding */ DEFAULT_REQUEST_TIMEOUT_MSEC),\n/* harmony export */   Protocol: () => (/* binding */ Protocol),\n/* harmony export */   mergeCapabilities: () => (/* binding */ mergeCapabilities)\n/* harmony export */ });\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types.js */ \"(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/types.js\");\n\n/**\n * The default request timeout, in miliseconds.\n */\nconst DEFAULT_REQUEST_TIMEOUT_MSEC = 60000;\n/**\n * Implements MCP protocol framing on top of a pluggable transport, including\n * features like request/response linking, notifications, and progress.\n */\nclass Protocol {\n    constructor(_options) {\n        this._options = _options;\n        this._requestMessageId = 0;\n        this._requestHandlers = new Map();\n        this._requestHandlerAbortControllers = new Map();\n        this._notificationHandlers = new Map();\n        this._responseHandlers = new Map();\n        this._progressHandlers = new Map();\n        this._timeoutInfo = new Map();\n        this.setNotificationHandler(_types_js__WEBPACK_IMPORTED_MODULE_0__.CancelledNotificationSchema, (notification) => {\n            const controller = this._requestHandlerAbortControllers.get(notification.params.requestId);\n            controller === null || controller === void 0 ? void 0 : controller.abort(notification.params.reason);\n        });\n        this.setNotificationHandler(_types_js__WEBPACK_IMPORTED_MODULE_0__.ProgressNotificationSchema, (notification) => {\n            this._onprogress(notification);\n        });\n        this.setRequestHandler(_types_js__WEBPACK_IMPORTED_MODULE_0__.PingRequestSchema, \n        // Automatic pong by default.\n        (_request) => ({}));\n    }\n    _setupTimeout(messageId, timeout, maxTotalTimeout, onTimeout, resetTimeoutOnProgress = false) {\n        this._timeoutInfo.set(messageId, {\n            timeoutId: setTimeout(onTimeout, timeout),\n            startTime: Date.now(),\n            timeout,\n            maxTotalTimeout,\n            resetTimeoutOnProgress,\n            onTimeout\n        });\n    }\n    _resetTimeout(messageId) {\n        const info = this._timeoutInfo.get(messageId);\n        if (!info)\n            return false;\n        const totalElapsed = Date.now() - info.startTime;\n        if (info.maxTotalTimeout && totalElapsed >= info.maxTotalTimeout) {\n            this._timeoutInfo.delete(messageId);\n            throw new _types_js__WEBPACK_IMPORTED_MODULE_0__.McpError(_types_js__WEBPACK_IMPORTED_MODULE_0__.ErrorCode.RequestTimeout, \"Maximum total timeout exceeded\", { maxTotalTimeout: info.maxTotalTimeout, totalElapsed });\n        }\n        clearTimeout(info.timeoutId);\n        info.timeoutId = setTimeout(info.onTimeout, info.timeout);\n        return true;\n    }\n    _cleanupTimeout(messageId) {\n        const info = this._timeoutInfo.get(messageId);\n        if (info) {\n            clearTimeout(info.timeoutId);\n            this._timeoutInfo.delete(messageId);\n        }\n    }\n    /**\n     * Attaches to the given transport, starts it, and starts listening for messages.\n     *\n     * The Protocol object assumes ownership of the Transport, replacing any callbacks that have already been set, and expects that it is the only user of the Transport instance going forward.\n     */\n    async connect(transport) {\n        var _a, _b, _c;\n        this._transport = transport;\n        const _onclose = (_a = this.transport) === null || _a === void 0 ? void 0 : _a.onclose;\n        this._transport.onclose = () => {\n            _onclose === null || _onclose === void 0 ? void 0 : _onclose();\n            this._onclose();\n        };\n        const _onerror = (_b = this.transport) === null || _b === void 0 ? void 0 : _b.onerror;\n        this._transport.onerror = (error) => {\n            _onerror === null || _onerror === void 0 ? void 0 : _onerror(error);\n            this._onerror(error);\n        };\n        const _onmessage = (_c = this._transport) === null || _c === void 0 ? void 0 : _c.onmessage;\n        this._transport.onmessage = (message, extra) => {\n            _onmessage === null || _onmessage === void 0 ? void 0 : _onmessage(message, extra);\n            if ((0,_types_js__WEBPACK_IMPORTED_MODULE_0__.isJSONRPCResponse)(message) || (0,_types_js__WEBPACK_IMPORTED_MODULE_0__.isJSONRPCError)(message)) {\n                this._onresponse(message);\n            }\n            else if ((0,_types_js__WEBPACK_IMPORTED_MODULE_0__.isJSONRPCRequest)(message)) {\n                this._onrequest(message, extra);\n            }\n            else if ((0,_types_js__WEBPACK_IMPORTED_MODULE_0__.isJSONRPCNotification)(message)) {\n                this._onnotification(message);\n            }\n            else {\n                this._onerror(new Error(`Unknown message type: ${JSON.stringify(message)}`));\n            }\n        };\n        await this._transport.start();\n    }\n    _onclose() {\n        var _a;\n        const responseHandlers = this._responseHandlers;\n        this._responseHandlers = new Map();\n        this._progressHandlers.clear();\n        this._transport = undefined;\n        (_a = this.onclose) === null || _a === void 0 ? void 0 : _a.call(this);\n        const error = new _types_js__WEBPACK_IMPORTED_MODULE_0__.McpError(_types_js__WEBPACK_IMPORTED_MODULE_0__.ErrorCode.ConnectionClosed, \"Connection closed\");\n        for (const handler of responseHandlers.values()) {\n            handler(error);\n        }\n    }\n    _onerror(error) {\n        var _a;\n        (_a = this.onerror) === null || _a === void 0 ? void 0 : _a.call(this, error);\n    }\n    _onnotification(notification) {\n        var _a;\n        const handler = (_a = this._notificationHandlers.get(notification.method)) !== null && _a !== void 0 ? _a : this.fallbackNotificationHandler;\n        // Ignore notifications not being subscribed to.\n        if (handler === undefined) {\n            return;\n        }\n        // Starting with Promise.resolve() puts any synchronous errors into the monad as well.\n        Promise.resolve()\n            .then(() => handler(notification))\n            .catch((error) => this._onerror(new Error(`Uncaught error in notification handler: ${error}`)));\n    }\n    _onrequest(request, extra) {\n        var _a, _b, _c, _d;\n        const handler = (_a = this._requestHandlers.get(request.method)) !== null && _a !== void 0 ? _a : this.fallbackRequestHandler;\n        if (handler === undefined) {\n            (_b = this._transport) === null || _b === void 0 ? void 0 : _b.send({\n                jsonrpc: \"2.0\",\n                id: request.id,\n                error: {\n                    code: _types_js__WEBPACK_IMPORTED_MODULE_0__.ErrorCode.MethodNotFound,\n                    message: \"Method not found\",\n                },\n            }).catch((error) => this._onerror(new Error(`Failed to send an error response: ${error}`)));\n            return;\n        }\n        const abortController = new AbortController();\n        this._requestHandlerAbortControllers.set(request.id, abortController);\n        const fullExtra = {\n            signal: abortController.signal,\n            sessionId: (_c = this._transport) === null || _c === void 0 ? void 0 : _c.sessionId,\n            _meta: (_d = request.params) === null || _d === void 0 ? void 0 : _d._meta,\n            sendNotification: (notification) => this.notification(notification, { relatedRequestId: request.id }),\n            sendRequest: (r, resultSchema, options) => this.request(r, resultSchema, { ...options, relatedRequestId: request.id }),\n            authInfo: extra === null || extra === void 0 ? void 0 : extra.authInfo,\n            requestId: request.id,\n            requestInfo: extra === null || extra === void 0 ? void 0 : extra.requestInfo\n        };\n        // Starting with Promise.resolve() puts any synchronous errors into the monad as well.\n        Promise.resolve()\n            .then(() => handler(request, fullExtra))\n            .then((result) => {\n            var _a;\n            if (abortController.signal.aborted) {\n                return;\n            }\n            return (_a = this._transport) === null || _a === void 0 ? void 0 : _a.send({\n                result,\n                jsonrpc: \"2.0\",\n                id: request.id,\n            });\n        }, (error) => {\n            var _a, _b;\n            if (abortController.signal.aborted) {\n                return;\n            }\n            return (_a = this._transport) === null || _a === void 0 ? void 0 : _a.send({\n                jsonrpc: \"2.0\",\n                id: request.id,\n                error: {\n                    code: Number.isSafeInteger(error[\"code\"])\n                        ? error[\"code\"]\n                        : _types_js__WEBPACK_IMPORTED_MODULE_0__.ErrorCode.InternalError,\n                    message: (_b = error.message) !== null && _b !== void 0 ? _b : \"Internal error\",\n                },\n            });\n        })\n            .catch((error) => this._onerror(new Error(`Failed to send response: ${error}`)))\n            .finally(() => {\n            this._requestHandlerAbortControllers.delete(request.id);\n        });\n    }\n    _onprogress(notification) {\n        const { progressToken, ...params } = notification.params;\n        const messageId = Number(progressToken);\n        const handler = this._progressHandlers.get(messageId);\n        if (!handler) {\n            this._onerror(new Error(`Received a progress notification for an unknown token: ${JSON.stringify(notification)}`));\n            return;\n        }\n        const responseHandler = this._responseHandlers.get(messageId);\n        const timeoutInfo = this._timeoutInfo.get(messageId);\n        if (timeoutInfo && responseHandler && timeoutInfo.resetTimeoutOnProgress) {\n            try {\n                this._resetTimeout(messageId);\n            }\n            catch (error) {\n                responseHandler(error);\n                return;\n            }\n        }\n        handler(params);\n    }\n    _onresponse(response) {\n        const messageId = Number(response.id);\n        const handler = this._responseHandlers.get(messageId);\n        if (handler === undefined) {\n            this._onerror(new Error(`Received a response for an unknown message ID: ${JSON.stringify(response)}`));\n            return;\n        }\n        this._responseHandlers.delete(messageId);\n        this._progressHandlers.delete(messageId);\n        this._cleanupTimeout(messageId);\n        if ((0,_types_js__WEBPACK_IMPORTED_MODULE_0__.isJSONRPCResponse)(response)) {\n            handler(response);\n        }\n        else {\n            const error = new _types_js__WEBPACK_IMPORTED_MODULE_0__.McpError(response.error.code, response.error.message, response.error.data);\n            handler(error);\n        }\n    }\n    get transport() {\n        return this._transport;\n    }\n    /**\n     * Closes the connection.\n     */\n    async close() {\n        var _a;\n        await ((_a = this._transport) === null || _a === void 0 ? void 0 : _a.close());\n    }\n    /**\n     * Sends a request and wait for a response.\n     *\n     * Do not use this method to emit notifications! Use notification() instead.\n     */\n    request(request, resultSchema, options) {\n        const { relatedRequestId, resumptionToken, onresumptiontoken } = options !== null && options !== void 0 ? options : {};\n        return new Promise((resolve, reject) => {\n            var _a, _b, _c, _d, _e, _f;\n            if (!this._transport) {\n                reject(new Error(\"Not connected\"));\n                return;\n            }\n            if (((_a = this._options) === null || _a === void 0 ? void 0 : _a.enforceStrictCapabilities) === true) {\n                this.assertCapabilityForMethod(request.method);\n            }\n            (_b = options === null || options === void 0 ? void 0 : options.signal) === null || _b === void 0 ? void 0 : _b.throwIfAborted();\n            const messageId = this._requestMessageId++;\n            const jsonrpcRequest = {\n                ...request,\n                jsonrpc: \"2.0\",\n                id: messageId,\n            };\n            if (options === null || options === void 0 ? void 0 : options.onprogress) {\n                this._progressHandlers.set(messageId, options.onprogress);\n                jsonrpcRequest.params = {\n                    ...request.params,\n                    _meta: {\n                        ...(((_c = request.params) === null || _c === void 0 ? void 0 : _c._meta) || {}),\n                        progressToken: messageId\n                    },\n                };\n            }\n            const cancel = (reason) => {\n                var _a;\n                this._responseHandlers.delete(messageId);\n                this._progressHandlers.delete(messageId);\n                this._cleanupTimeout(messageId);\n                (_a = this._transport) === null || _a === void 0 ? void 0 : _a.send({\n                    jsonrpc: \"2.0\",\n                    method: \"notifications/cancelled\",\n                    params: {\n                        requestId: messageId,\n                        reason: String(reason),\n                    },\n                }, { relatedRequestId, resumptionToken, onresumptiontoken }).catch((error) => this._onerror(new Error(`Failed to send cancellation: ${error}`)));\n                reject(reason);\n            };\n            this._responseHandlers.set(messageId, (response) => {\n                var _a;\n                if ((_a = options === null || options === void 0 ? void 0 : options.signal) === null || _a === void 0 ? void 0 : _a.aborted) {\n                    return;\n                }\n                if (response instanceof Error) {\n                    return reject(response);\n                }\n                try {\n                    const result = resultSchema.parse(response.result);\n                    resolve(result);\n                }\n                catch (error) {\n                    reject(error);\n                }\n            });\n            (_d = options === null || options === void 0 ? void 0 : options.signal) === null || _d === void 0 ? void 0 : _d.addEventListener(\"abort\", () => {\n                var _a;\n                cancel((_a = options === null || options === void 0 ? void 0 : options.signal) === null || _a === void 0 ? void 0 : _a.reason);\n            });\n            const timeout = (_e = options === null || options === void 0 ? void 0 : options.timeout) !== null && _e !== void 0 ? _e : DEFAULT_REQUEST_TIMEOUT_MSEC;\n            const timeoutHandler = () => cancel(new _types_js__WEBPACK_IMPORTED_MODULE_0__.McpError(_types_js__WEBPACK_IMPORTED_MODULE_0__.ErrorCode.RequestTimeout, \"Request timed out\", { timeout }));\n            this._setupTimeout(messageId, timeout, options === null || options === void 0 ? void 0 : options.maxTotalTimeout, timeoutHandler, (_f = options === null || options === void 0 ? void 0 : options.resetTimeoutOnProgress) !== null && _f !== void 0 ? _f : false);\n            this._transport.send(jsonrpcRequest, { relatedRequestId, resumptionToken, onresumptiontoken }).catch((error) => {\n                this._cleanupTimeout(messageId);\n                reject(error);\n            });\n        });\n    }\n    /**\n     * Emits a notification, which is a one-way message that does not expect a response.\n     */\n    async notification(notification, options) {\n        if (!this._transport) {\n            throw new Error(\"Not connected\");\n        }\n        this.assertNotificationCapability(notification.method);\n        const jsonrpcNotification = {\n            ...notification,\n            jsonrpc: \"2.0\",\n        };\n        await this._transport.send(jsonrpcNotification, options);\n    }\n    /**\n     * Registers a handler to invoke when this protocol object receives a request with the given method.\n     *\n     * Note that this will replace any previous request handler for the same method.\n     */\n    setRequestHandler(requestSchema, handler) {\n        const method = requestSchema.shape.method.value;\n        this.assertRequestHandlerCapability(method);\n        this._requestHandlers.set(method, (request, extra) => {\n            return Promise.resolve(handler(requestSchema.parse(request), extra));\n        });\n    }\n    /**\n     * Removes the request handler for the given method.\n     */\n    removeRequestHandler(method) {\n        this._requestHandlers.delete(method);\n    }\n    /**\n     * Asserts that a request handler has not already been set for the given method, in preparation for a new one being automatically installed.\n     */\n    assertCanSetRequestHandler(method) {\n        if (this._requestHandlers.has(method)) {\n            throw new Error(`A request handler for ${method} already exists, which would be overridden`);\n        }\n    }\n    /**\n     * Registers a handler to invoke when this protocol object receives a notification with the given method.\n     *\n     * Note that this will replace any previous notification handler for the same method.\n     */\n    setNotificationHandler(notificationSchema, handler) {\n        this._notificationHandlers.set(notificationSchema.shape.method.value, (notification) => Promise.resolve(handler(notificationSchema.parse(notification))));\n    }\n    /**\n     * Removes the notification handler for the given method.\n     */\n    removeNotificationHandler(method) {\n        this._notificationHandlers.delete(method);\n    }\n}\nfunction mergeCapabilities(base, additional) {\n    return Object.entries(additional).reduce((acc, [key, value]) => {\n        if (value && typeof value === \"object\") {\n            acc[key] = acc[key] ? { ...acc[key], ...value } : value;\n        }\n        else {\n            acc[key] = value;\n        }\n        return acc;\n    }, { ...base });\n}\n//# sourceMappingURL=protocol.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/shared/protocol.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/shared/stdio.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@modelcontextprotocol/sdk/dist/esm/shared/stdio.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReadBuffer: () => (/* binding */ ReadBuffer),\n/* harmony export */   deserializeMessage: () => (/* binding */ deserializeMessage),\n/* harmony export */   serializeMessage: () => (/* binding */ serializeMessage)\n/* harmony export */ });\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types.js */ \"(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/types.js\");\n\n/**\n * Buffers a continuous stdio stream into discrete JSON-RPC messages.\n */\nclass ReadBuffer {\n    append(chunk) {\n        this._buffer = this._buffer ? Buffer.concat([this._buffer, chunk]) : chunk;\n    }\n    readMessage() {\n        if (!this._buffer) {\n            return null;\n        }\n        const index = this._buffer.indexOf(\"\\n\");\n        if (index === -1) {\n            return null;\n        }\n        const line = this._buffer.toString(\"utf8\", 0, index).replace(/\\r$/, '');\n        this._buffer = this._buffer.subarray(index + 1);\n        return deserializeMessage(line);\n    }\n    clear() {\n        this._buffer = undefined;\n    }\n}\nfunction deserializeMessage(line) {\n    return _types_js__WEBPACK_IMPORTED_MODULE_0__.JSONRPCMessageSchema.parse(JSON.parse(line));\n}\nfunction serializeMessage(message) {\n    return JSON.stringify(message) + \"\\n\";\n}\n//# sourceMappingURL=stdio.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQG1vZGVsY29udGV4dHByb3RvY29sL3Nkay9kaXN0L2VzbS9zaGFyZWQvc3RkaW8uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFtRDtBQUNuRDtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUCxXQUFXLDJEQUFvQjtBQUMvQjtBQUNPO0FBQ1A7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXDIwMDIxNzkyXFxEb2N1bWVudHNcXFByb2plY3RzXFxDaGF0XFxub2RlX21vZHVsZXNcXEBtb2RlbGNvbnRleHRwcm90b2NvbFxcc2RrXFxkaXN0XFxlc21cXHNoYXJlZFxcc3RkaW8uanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgSlNPTlJQQ01lc3NhZ2VTY2hlbWEgfSBmcm9tIFwiLi4vdHlwZXMuanNcIjtcbi8qKlxuICogQnVmZmVycyBhIGNvbnRpbnVvdXMgc3RkaW8gc3RyZWFtIGludG8gZGlzY3JldGUgSlNPTi1SUEMgbWVzc2FnZXMuXG4gKi9cbmV4cG9ydCBjbGFzcyBSZWFkQnVmZmVyIHtcbiAgICBhcHBlbmQoY2h1bmspIHtcbiAgICAgICAgdGhpcy5fYnVmZmVyID0gdGhpcy5fYnVmZmVyID8gQnVmZmVyLmNvbmNhdChbdGhpcy5fYnVmZmVyLCBjaHVua10pIDogY2h1bms7XG4gICAgfVxuICAgIHJlYWRNZXNzYWdlKCkge1xuICAgICAgICBpZiAoIXRoaXMuX2J1ZmZlcikge1xuICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgaW5kZXggPSB0aGlzLl9idWZmZXIuaW5kZXhPZihcIlxcblwiKTtcbiAgICAgICAgaWYgKGluZGV4ID09PSAtMSkge1xuICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgbGluZSA9IHRoaXMuX2J1ZmZlci50b1N0cmluZyhcInV0ZjhcIiwgMCwgaW5kZXgpLnJlcGxhY2UoL1xcciQvLCAnJyk7XG4gICAgICAgIHRoaXMuX2J1ZmZlciA9IHRoaXMuX2J1ZmZlci5zdWJhcnJheShpbmRleCArIDEpO1xuICAgICAgICByZXR1cm4gZGVzZXJpYWxpemVNZXNzYWdlKGxpbmUpO1xuICAgIH1cbiAgICBjbGVhcigpIHtcbiAgICAgICAgdGhpcy5fYnVmZmVyID0gdW5kZWZpbmVkO1xuICAgIH1cbn1cbmV4cG9ydCBmdW5jdGlvbiBkZXNlcmlhbGl6ZU1lc3NhZ2UobGluZSkge1xuICAgIHJldHVybiBKU09OUlBDTWVzc2FnZVNjaGVtYS5wYXJzZShKU09OLnBhcnNlKGxpbmUpKTtcbn1cbmV4cG9ydCBmdW5jdGlvbiBzZXJpYWxpemVNZXNzYWdlKG1lc3NhZ2UpIHtcbiAgICByZXR1cm4gSlNPTi5zdHJpbmdpZnkobWVzc2FnZSkgKyBcIlxcblwiO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c3RkaW8uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/shared/stdio.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/types.js":
/*!******************************************************************!*\
  !*** ./node_modules/@modelcontextprotocol/sdk/dist/esm/types.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AudioContentSchema: () => (/* binding */ AudioContentSchema),\n/* harmony export */   BaseMetadataSchema: () => (/* binding */ BaseMetadataSchema),\n/* harmony export */   BlobResourceContentsSchema: () => (/* binding */ BlobResourceContentsSchema),\n/* harmony export */   BooleanSchemaSchema: () => (/* binding */ BooleanSchemaSchema),\n/* harmony export */   CallToolRequestSchema: () => (/* binding */ CallToolRequestSchema),\n/* harmony export */   CallToolResultSchema: () => (/* binding */ CallToolResultSchema),\n/* harmony export */   CancelledNotificationSchema: () => (/* binding */ CancelledNotificationSchema),\n/* harmony export */   ClientCapabilitiesSchema: () => (/* binding */ ClientCapabilitiesSchema),\n/* harmony export */   ClientNotificationSchema: () => (/* binding */ ClientNotificationSchema),\n/* harmony export */   ClientRequestSchema: () => (/* binding */ ClientRequestSchema),\n/* harmony export */   ClientResultSchema: () => (/* binding */ ClientResultSchema),\n/* harmony export */   CompatibilityCallToolResultSchema: () => (/* binding */ CompatibilityCallToolResultSchema),\n/* harmony export */   CompleteRequestSchema: () => (/* binding */ CompleteRequestSchema),\n/* harmony export */   CompleteResultSchema: () => (/* binding */ CompleteResultSchema),\n/* harmony export */   ContentBlockSchema: () => (/* binding */ ContentBlockSchema),\n/* harmony export */   CreateMessageRequestSchema: () => (/* binding */ CreateMessageRequestSchema),\n/* harmony export */   CreateMessageResultSchema: () => (/* binding */ CreateMessageResultSchema),\n/* harmony export */   CursorSchema: () => (/* binding */ CursorSchema),\n/* harmony export */   DEFAULT_NEGOTIATED_PROTOCOL_VERSION: () => (/* binding */ DEFAULT_NEGOTIATED_PROTOCOL_VERSION),\n/* harmony export */   ElicitRequestSchema: () => (/* binding */ ElicitRequestSchema),\n/* harmony export */   ElicitResultSchema: () => (/* binding */ ElicitResultSchema),\n/* harmony export */   EmbeddedResourceSchema: () => (/* binding */ EmbeddedResourceSchema),\n/* harmony export */   EmptyResultSchema: () => (/* binding */ EmptyResultSchema),\n/* harmony export */   EnumSchemaSchema: () => (/* binding */ EnumSchemaSchema),\n/* harmony export */   ErrorCode: () => (/* binding */ ErrorCode),\n/* harmony export */   GetPromptRequestSchema: () => (/* binding */ GetPromptRequestSchema),\n/* harmony export */   GetPromptResultSchema: () => (/* binding */ GetPromptResultSchema),\n/* harmony export */   ImageContentSchema: () => (/* binding */ ImageContentSchema),\n/* harmony export */   ImplementationSchema: () => (/* binding */ ImplementationSchema),\n/* harmony export */   InitializeRequestSchema: () => (/* binding */ InitializeRequestSchema),\n/* harmony export */   InitializeResultSchema: () => (/* binding */ InitializeResultSchema),\n/* harmony export */   InitializedNotificationSchema: () => (/* binding */ InitializedNotificationSchema),\n/* harmony export */   JSONRPCErrorSchema: () => (/* binding */ JSONRPCErrorSchema),\n/* harmony export */   JSONRPCMessageSchema: () => (/* binding */ JSONRPCMessageSchema),\n/* harmony export */   JSONRPCNotificationSchema: () => (/* binding */ JSONRPCNotificationSchema),\n/* harmony export */   JSONRPCRequestSchema: () => (/* binding */ JSONRPCRequestSchema),\n/* harmony export */   JSONRPCResponseSchema: () => (/* binding */ JSONRPCResponseSchema),\n/* harmony export */   JSONRPC_VERSION: () => (/* binding */ JSONRPC_VERSION),\n/* harmony export */   LATEST_PROTOCOL_VERSION: () => (/* binding */ LATEST_PROTOCOL_VERSION),\n/* harmony export */   ListPromptsRequestSchema: () => (/* binding */ ListPromptsRequestSchema),\n/* harmony export */   ListPromptsResultSchema: () => (/* binding */ ListPromptsResultSchema),\n/* harmony export */   ListResourceTemplatesRequestSchema: () => (/* binding */ ListResourceTemplatesRequestSchema),\n/* harmony export */   ListResourceTemplatesResultSchema: () => (/* binding */ ListResourceTemplatesResultSchema),\n/* harmony export */   ListResourcesRequestSchema: () => (/* binding */ ListResourcesRequestSchema),\n/* harmony export */   ListResourcesResultSchema: () => (/* binding */ ListResourcesResultSchema),\n/* harmony export */   ListRootsRequestSchema: () => (/* binding */ ListRootsRequestSchema),\n/* harmony export */   ListRootsResultSchema: () => (/* binding */ ListRootsResultSchema),\n/* harmony export */   ListToolsRequestSchema: () => (/* binding */ ListToolsRequestSchema),\n/* harmony export */   ListToolsResultSchema: () => (/* binding */ ListToolsResultSchema),\n/* harmony export */   LoggingLevelSchema: () => (/* binding */ LoggingLevelSchema),\n/* harmony export */   LoggingMessageNotificationSchema: () => (/* binding */ LoggingMessageNotificationSchema),\n/* harmony export */   McpError: () => (/* binding */ McpError),\n/* harmony export */   ModelHintSchema: () => (/* binding */ ModelHintSchema),\n/* harmony export */   ModelPreferencesSchema: () => (/* binding */ ModelPreferencesSchema),\n/* harmony export */   NotificationSchema: () => (/* binding */ NotificationSchema),\n/* harmony export */   NumberSchemaSchema: () => (/* binding */ NumberSchemaSchema),\n/* harmony export */   PaginatedRequestSchema: () => (/* binding */ PaginatedRequestSchema),\n/* harmony export */   PaginatedResultSchema: () => (/* binding */ PaginatedResultSchema),\n/* harmony export */   PingRequestSchema: () => (/* binding */ PingRequestSchema),\n/* harmony export */   PrimitiveSchemaDefinitionSchema: () => (/* binding */ PrimitiveSchemaDefinitionSchema),\n/* harmony export */   ProgressNotificationSchema: () => (/* binding */ ProgressNotificationSchema),\n/* harmony export */   ProgressSchema: () => (/* binding */ ProgressSchema),\n/* harmony export */   ProgressTokenSchema: () => (/* binding */ ProgressTokenSchema),\n/* harmony export */   PromptArgumentSchema: () => (/* binding */ PromptArgumentSchema),\n/* harmony export */   PromptListChangedNotificationSchema: () => (/* binding */ PromptListChangedNotificationSchema),\n/* harmony export */   PromptMessageSchema: () => (/* binding */ PromptMessageSchema),\n/* harmony export */   PromptReferenceSchema: () => (/* binding */ PromptReferenceSchema),\n/* harmony export */   PromptSchema: () => (/* binding */ PromptSchema),\n/* harmony export */   ReadResourceRequestSchema: () => (/* binding */ ReadResourceRequestSchema),\n/* harmony export */   ReadResourceResultSchema: () => (/* binding */ ReadResourceResultSchema),\n/* harmony export */   RequestIdSchema: () => (/* binding */ RequestIdSchema),\n/* harmony export */   RequestSchema: () => (/* binding */ RequestSchema),\n/* harmony export */   ResourceContentsSchema: () => (/* binding */ ResourceContentsSchema),\n/* harmony export */   ResourceLinkSchema: () => (/* binding */ ResourceLinkSchema),\n/* harmony export */   ResourceListChangedNotificationSchema: () => (/* binding */ ResourceListChangedNotificationSchema),\n/* harmony export */   ResourceReferenceSchema: () => (/* binding */ ResourceReferenceSchema),\n/* harmony export */   ResourceSchema: () => (/* binding */ ResourceSchema),\n/* harmony export */   ResourceTemplateReferenceSchema: () => (/* binding */ ResourceTemplateReferenceSchema),\n/* harmony export */   ResourceTemplateSchema: () => (/* binding */ ResourceTemplateSchema),\n/* harmony export */   ResourceUpdatedNotificationSchema: () => (/* binding */ ResourceUpdatedNotificationSchema),\n/* harmony export */   ResultSchema: () => (/* binding */ ResultSchema),\n/* harmony export */   RootSchema: () => (/* binding */ RootSchema),\n/* harmony export */   RootsListChangedNotificationSchema: () => (/* binding */ RootsListChangedNotificationSchema),\n/* harmony export */   SUPPORTED_PROTOCOL_VERSIONS: () => (/* binding */ SUPPORTED_PROTOCOL_VERSIONS),\n/* harmony export */   SamplingMessageSchema: () => (/* binding */ SamplingMessageSchema),\n/* harmony export */   ServerCapabilitiesSchema: () => (/* binding */ ServerCapabilitiesSchema),\n/* harmony export */   ServerNotificationSchema: () => (/* binding */ ServerNotificationSchema),\n/* harmony export */   ServerRequestSchema: () => (/* binding */ ServerRequestSchema),\n/* harmony export */   ServerResultSchema: () => (/* binding */ ServerResultSchema),\n/* harmony export */   SetLevelRequestSchema: () => (/* binding */ SetLevelRequestSchema),\n/* harmony export */   StringSchemaSchema: () => (/* binding */ StringSchemaSchema),\n/* harmony export */   SubscribeRequestSchema: () => (/* binding */ SubscribeRequestSchema),\n/* harmony export */   TextContentSchema: () => (/* binding */ TextContentSchema),\n/* harmony export */   TextResourceContentsSchema: () => (/* binding */ TextResourceContentsSchema),\n/* harmony export */   ToolAnnotationsSchema: () => (/* binding */ ToolAnnotationsSchema),\n/* harmony export */   ToolListChangedNotificationSchema: () => (/* binding */ ToolListChangedNotificationSchema),\n/* harmony export */   ToolSchema: () => (/* binding */ ToolSchema),\n/* harmony export */   UnsubscribeRequestSchema: () => (/* binding */ UnsubscribeRequestSchema),\n/* harmony export */   isInitializeRequest: () => (/* binding */ isInitializeRequest),\n/* harmony export */   isInitializedNotification: () => (/* binding */ isInitializedNotification),\n/* harmony export */   isJSONRPCError: () => (/* binding */ isJSONRPCError),\n/* harmony export */   isJSONRPCNotification: () => (/* binding */ isJSONRPCNotification),\n/* harmony export */   isJSONRPCRequest: () => (/* binding */ isJSONRPCRequest),\n/* harmony export */   isJSONRPCResponse: () => (/* binding */ isJSONRPCResponse)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n\nconst LATEST_PROTOCOL_VERSION = \"2025-06-18\";\nconst DEFAULT_NEGOTIATED_PROTOCOL_VERSION = \"2025-03-26\";\nconst SUPPORTED_PROTOCOL_VERSIONS = [\n    LATEST_PROTOCOL_VERSION,\n    \"2025-03-26\",\n    \"2024-11-05\",\n    \"2024-10-07\",\n];\n/* JSON-RPC types */\nconst JSONRPC_VERSION = \"2.0\";\n/**\n * A progress token, used to associate progress notifications with the original request.\n */\nconst ProgressTokenSchema = zod__WEBPACK_IMPORTED_MODULE_0__.union([zod__WEBPACK_IMPORTED_MODULE_0__.string(), zod__WEBPACK_IMPORTED_MODULE_0__.number().int()]);\n/**\n * An opaque token used to represent a cursor for pagination.\n */\nconst CursorSchema = zod__WEBPACK_IMPORTED_MODULE_0__.string();\nconst RequestMetaSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    /**\n     * If specified, the caller is requesting out-of-band progress notifications for this request (as represented by notifications/progress). The value of this parameter is an opaque token that will be attached to any subsequent notifications. The receiver is not obligated to provide these notifications.\n     */\n    progressToken: zod__WEBPACK_IMPORTED_MODULE_0__.optional(ProgressTokenSchema),\n})\n    .passthrough();\nconst BaseRequestParamsSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    _meta: zod__WEBPACK_IMPORTED_MODULE_0__.optional(RequestMetaSchema),\n})\n    .passthrough();\nconst RequestSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n    params: zod__WEBPACK_IMPORTED_MODULE_0__.optional(BaseRequestParamsSchema),\n});\nconst BaseNotificationParamsSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    /**\n     * See [MCP specification](https://github.com/modelcontextprotocol/modelcontextprotocol/blob/47339c03c143bb4ec01a26e721a1b8fe66634ebe/docs/specification/draft/basic/index.mdx#general-fields)\n     * for notes on _meta usage.\n     */\n    _meta: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.object({}).passthrough()),\n})\n    .passthrough();\nconst NotificationSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n    params: zod__WEBPACK_IMPORTED_MODULE_0__.optional(BaseNotificationParamsSchema),\n});\nconst ResultSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    /**\n     * See [MCP specification](https://github.com/modelcontextprotocol/modelcontextprotocol/blob/47339c03c143bb4ec01a26e721a1b8fe66634ebe/docs/specification/draft/basic/index.mdx#general-fields)\n     * for notes on _meta usage.\n     */\n    _meta: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.object({}).passthrough()),\n})\n    .passthrough();\n/**\n * A uniquely identifying ID for a request in JSON-RPC.\n */\nconst RequestIdSchema = zod__WEBPACK_IMPORTED_MODULE_0__.union([zod__WEBPACK_IMPORTED_MODULE_0__.string(), zod__WEBPACK_IMPORTED_MODULE_0__.number().int()]);\n/**\n * A request that expects a response.\n */\nconst JSONRPCRequestSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    jsonrpc: zod__WEBPACK_IMPORTED_MODULE_0__.literal(JSONRPC_VERSION),\n    id: RequestIdSchema,\n})\n    .merge(RequestSchema)\n    .strict();\nconst isJSONRPCRequest = (value) => JSONRPCRequestSchema.safeParse(value).success;\n/**\n * A notification which does not expect a response.\n */\nconst JSONRPCNotificationSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    jsonrpc: zod__WEBPACK_IMPORTED_MODULE_0__.literal(JSONRPC_VERSION),\n})\n    .merge(NotificationSchema)\n    .strict();\nconst isJSONRPCNotification = (value) => JSONRPCNotificationSchema.safeParse(value).success;\n/**\n * A successful (non-error) response to a request.\n */\nconst JSONRPCResponseSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    jsonrpc: zod__WEBPACK_IMPORTED_MODULE_0__.literal(JSONRPC_VERSION),\n    id: RequestIdSchema,\n    result: ResultSchema,\n})\n    .strict();\nconst isJSONRPCResponse = (value) => JSONRPCResponseSchema.safeParse(value).success;\n/**\n * Error codes defined by the JSON-RPC specification.\n */\nvar ErrorCode;\n(function (ErrorCode) {\n    // SDK error codes\n    ErrorCode[ErrorCode[\"ConnectionClosed\"] = -32000] = \"ConnectionClosed\";\n    ErrorCode[ErrorCode[\"RequestTimeout\"] = -32001] = \"RequestTimeout\";\n    // Standard JSON-RPC error codes\n    ErrorCode[ErrorCode[\"ParseError\"] = -32700] = \"ParseError\";\n    ErrorCode[ErrorCode[\"InvalidRequest\"] = -32600] = \"InvalidRequest\";\n    ErrorCode[ErrorCode[\"MethodNotFound\"] = -32601] = \"MethodNotFound\";\n    ErrorCode[ErrorCode[\"InvalidParams\"] = -32602] = \"InvalidParams\";\n    ErrorCode[ErrorCode[\"InternalError\"] = -32603] = \"InternalError\";\n})(ErrorCode || (ErrorCode = {}));\n/**\n * A response to a request that indicates an error occurred.\n */\nconst JSONRPCErrorSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    jsonrpc: zod__WEBPACK_IMPORTED_MODULE_0__.literal(JSONRPC_VERSION),\n    id: RequestIdSchema,\n    error: zod__WEBPACK_IMPORTED_MODULE_0__.object({\n        /**\n         * The error type that occurred.\n         */\n        code: zod__WEBPACK_IMPORTED_MODULE_0__.number().int(),\n        /**\n         * A short description of the error. The message SHOULD be limited to a concise single sentence.\n         */\n        message: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        /**\n         * Additional information about the error. The value of this member is defined by the sender (e.g. detailed error information, nested errors etc.).\n         */\n        data: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.unknown()),\n    }),\n})\n    .strict();\nconst isJSONRPCError = (value) => JSONRPCErrorSchema.safeParse(value).success;\nconst JSONRPCMessageSchema = zod__WEBPACK_IMPORTED_MODULE_0__.union([\n    JSONRPCRequestSchema,\n    JSONRPCNotificationSchema,\n    JSONRPCResponseSchema,\n    JSONRPCErrorSchema,\n]);\n/* Empty result */\n/**\n * A response that indicates success but carries no data.\n */\nconst EmptyResultSchema = ResultSchema.strict();\n/* Cancellation */\n/**\n * This notification can be sent by either side to indicate that it is cancelling a previously-issued request.\n *\n * The request SHOULD still be in-flight, but due to communication latency, it is always possible that this notification MAY arrive after the request has already finished.\n *\n * This notification indicates that the result will be unused, so any associated processing SHOULD cease.\n *\n * A client MUST NOT attempt to cancel its `initialize` request.\n */\nconst CancelledNotificationSchema = NotificationSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"notifications/cancelled\"),\n    params: BaseNotificationParamsSchema.extend({\n        /**\n         * The ID of the request to cancel.\n         *\n         * This MUST correspond to the ID of a request previously issued in the same direction.\n         */\n        requestId: RequestIdSchema,\n        /**\n         * An optional string describing the reason for the cancellation. This MAY be logged or presented to the user.\n         */\n        reason: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    }),\n});\n/* Base Metadata */\n/**\n * Base metadata interface for common properties across resources, tools, prompts, and implementations.\n */\nconst BaseMetadataSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    /** Intended for programmatic or logical use, but used as a display name in past specs or fallback */\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n    /**\n    * Intended for UI and end-user contexts — optimized to be human-readable and easily understood,\n    * even by those unfamiliar with domain-specific terminology.\n    *\n    * If not provided, the name should be used for display (except for Tool,\n    * where `annotations.title` should be given precedence over using `name`,\n    * if present).\n    */\n    title: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.string()),\n})\n    .passthrough();\n/* Initialization */\n/**\n * Describes the name and version of an MCP implementation.\n */\nconst ImplementationSchema = BaseMetadataSchema.extend({\n    version: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n});\n/**\n * Capabilities a client may support. Known capabilities are defined here, in this schema, but this is not a closed set: any client can define its own, additional capabilities.\n */\nconst ClientCapabilitiesSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    /**\n     * Experimental, non-standard capabilities that the client supports.\n     */\n    experimental: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.object({}).passthrough()),\n    /**\n     * Present if the client supports sampling from an LLM.\n     */\n    sampling: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.object({}).passthrough()),\n    /**\n     * Present if the client supports eliciting user input.\n     */\n    elicitation: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.object({}).passthrough()),\n    /**\n     * Present if the client supports listing roots.\n     */\n    roots: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.object({\n        /**\n         * Whether the client supports issuing notifications for changes to the roots list.\n         */\n        listChanged: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.boolean()),\n    })\n        .passthrough()),\n})\n    .passthrough();\n/**\n * This request is sent from the client to the server when it first connects, asking it to begin initialization.\n */\nconst InitializeRequestSchema = RequestSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"initialize\"),\n    params: BaseRequestParamsSchema.extend({\n        /**\n         * The latest version of the Model Context Protocol that the client supports. The client MAY decide to support older versions as well.\n         */\n        protocolVersion: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        capabilities: ClientCapabilitiesSchema,\n        clientInfo: ImplementationSchema,\n    }),\n});\nconst isInitializeRequest = (value) => InitializeRequestSchema.safeParse(value).success;\n/**\n * Capabilities that a server may support. Known capabilities are defined here, in this schema, but this is not a closed set: any server can define its own, additional capabilities.\n */\nconst ServerCapabilitiesSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    /**\n     * Experimental, non-standard capabilities that the server supports.\n     */\n    experimental: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.object({}).passthrough()),\n    /**\n     * Present if the server supports sending log messages to the client.\n     */\n    logging: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.object({}).passthrough()),\n    /**\n     * Present if the server supports sending completions to the client.\n     */\n    completions: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.object({}).passthrough()),\n    /**\n     * Present if the server offers any prompt templates.\n     */\n    prompts: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.object({\n        /**\n         * Whether this server supports issuing notifications for changes to the prompt list.\n         */\n        listChanged: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.boolean()),\n    })\n        .passthrough()),\n    /**\n     * Present if the server offers any resources to read.\n     */\n    resources: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.object({\n        /**\n         * Whether this server supports clients subscribing to resource updates.\n         */\n        subscribe: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.boolean()),\n        /**\n         * Whether this server supports issuing notifications for changes to the resource list.\n         */\n        listChanged: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.boolean()),\n    })\n        .passthrough()),\n    /**\n     * Present if the server offers any tools to call.\n     */\n    tools: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.object({\n        /**\n         * Whether this server supports issuing notifications for changes to the tool list.\n         */\n        listChanged: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.boolean()),\n    })\n        .passthrough()),\n})\n    .passthrough();\n/**\n * After receiving an initialize request from the client, the server sends this response.\n */\nconst InitializeResultSchema = ResultSchema.extend({\n    /**\n     * The version of the Model Context Protocol that the server wants to use. This may not match the version that the client requested. If the client cannot support this version, it MUST disconnect.\n     */\n    protocolVersion: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n    capabilities: ServerCapabilitiesSchema,\n    serverInfo: ImplementationSchema,\n    /**\n     * Instructions describing how to use the server and its features.\n     *\n     * This can be used by clients to improve the LLM's understanding of available tools, resources, etc. It can be thought of like a \"hint\" to the model. For example, this information MAY be added to the system prompt.\n     */\n    instructions: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.string()),\n});\n/**\n * This notification is sent from the client to the server after initialization has finished.\n */\nconst InitializedNotificationSchema = NotificationSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"notifications/initialized\"),\n});\nconst isInitializedNotification = (value) => InitializedNotificationSchema.safeParse(value).success;\n/* Ping */\n/**\n * A ping, issued by either the server or the client, to check that the other party is still alive. The receiver must promptly respond, or else may be disconnected.\n */\nconst PingRequestSchema = RequestSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"ping\"),\n});\n/* Progress notifications */\nconst ProgressSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    /**\n     * The progress thus far. This should increase every time progress is made, even if the total is unknown.\n     */\n    progress: zod__WEBPACK_IMPORTED_MODULE_0__.number(),\n    /**\n     * Total number of items to process (or total progress required), if known.\n     */\n    total: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.number()),\n    /**\n     * An optional message describing the current progress.\n     */\n    message: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.string()),\n})\n    .passthrough();\n/**\n * An out-of-band notification used to inform the receiver of a progress update for a long-running request.\n */\nconst ProgressNotificationSchema = NotificationSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"notifications/progress\"),\n    params: BaseNotificationParamsSchema.merge(ProgressSchema).extend({\n        /**\n         * The progress token which was given in the initial request, used to associate this notification with the request that is proceeding.\n         */\n        progressToken: ProgressTokenSchema,\n    }),\n});\n/* Pagination */\nconst PaginatedRequestSchema = RequestSchema.extend({\n    params: BaseRequestParamsSchema.extend({\n        /**\n         * An opaque token representing the current pagination position.\n         * If provided, the server should return results starting after this cursor.\n         */\n        cursor: zod__WEBPACK_IMPORTED_MODULE_0__.optional(CursorSchema),\n    }).optional(),\n});\nconst PaginatedResultSchema = ResultSchema.extend({\n    /**\n     * An opaque token representing the pagination position after the last returned result.\n     * If present, there may be more results available.\n     */\n    nextCursor: zod__WEBPACK_IMPORTED_MODULE_0__.optional(CursorSchema),\n});\n/* Resources */\n/**\n * The contents of a specific resource or sub-resource.\n */\nconst ResourceContentsSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    /**\n     * The URI of this resource.\n     */\n    uri: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n    /**\n     * The MIME type of this resource, if known.\n     */\n    mimeType: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.string()),\n    /**\n     * See [MCP specification](https://github.com/modelcontextprotocol/modelcontextprotocol/blob/47339c03c143bb4ec01a26e721a1b8fe66634ebe/docs/specification/draft/basic/index.mdx#general-fields)\n     * for notes on _meta usage.\n     */\n    _meta: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.object({}).passthrough()),\n})\n    .passthrough();\nconst TextResourceContentsSchema = ResourceContentsSchema.extend({\n    /**\n     * The text of the item. This must only be set if the item can actually be represented as text (not binary data).\n     */\n    text: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n});\nconst BlobResourceContentsSchema = ResourceContentsSchema.extend({\n    /**\n     * A base64-encoded string representing the binary data of the item.\n     */\n    blob: zod__WEBPACK_IMPORTED_MODULE_0__.string().base64(),\n});\n/**\n * A known resource that the server is capable of reading.\n */\nconst ResourceSchema = BaseMetadataSchema.extend({\n    /**\n     * The URI of this resource.\n     */\n    uri: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n    /**\n     * A description of what this resource represents.\n     *\n     * This can be used by clients to improve the LLM's understanding of available resources. It can be thought of like a \"hint\" to the model.\n     */\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.string()),\n    /**\n     * The MIME type of this resource, if known.\n     */\n    mimeType: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.string()),\n    /**\n     * See [MCP specification](https://github.com/modelcontextprotocol/modelcontextprotocol/blob/47339c03c143bb4ec01a26e721a1b8fe66634ebe/docs/specification/draft/basic/index.mdx#general-fields)\n     * for notes on _meta usage.\n     */\n    _meta: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.object({}).passthrough()),\n});\n/**\n * A template description for resources available on the server.\n */\nconst ResourceTemplateSchema = BaseMetadataSchema.extend({\n    /**\n     * A URI template (according to RFC 6570) that can be used to construct resource URIs.\n     */\n    uriTemplate: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n    /**\n     * A description of what this template is for.\n     *\n     * This can be used by clients to improve the LLM's understanding of available resources. It can be thought of like a \"hint\" to the model.\n     */\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.string()),\n    /**\n     * The MIME type for all resources that match this template. This should only be included if all resources matching this template have the same type.\n     */\n    mimeType: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.string()),\n    /**\n     * See [MCP specification](https://github.com/modelcontextprotocol/modelcontextprotocol/blob/47339c03c143bb4ec01a26e721a1b8fe66634ebe/docs/specification/draft/basic/index.mdx#general-fields)\n     * for notes on _meta usage.\n     */\n    _meta: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.object({}).passthrough()),\n});\n/**\n * Sent from the client to request a list of resources the server has.\n */\nconst ListResourcesRequestSchema = PaginatedRequestSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"resources/list\"),\n});\n/**\n * The server's response to a resources/list request from the client.\n */\nconst ListResourcesResultSchema = PaginatedResultSchema.extend({\n    resources: zod__WEBPACK_IMPORTED_MODULE_0__.array(ResourceSchema),\n});\n/**\n * Sent from the client to request a list of resource templates the server has.\n */\nconst ListResourceTemplatesRequestSchema = PaginatedRequestSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"resources/templates/list\"),\n});\n/**\n * The server's response to a resources/templates/list request from the client.\n */\nconst ListResourceTemplatesResultSchema = PaginatedResultSchema.extend({\n    resourceTemplates: zod__WEBPACK_IMPORTED_MODULE_0__.array(ResourceTemplateSchema),\n});\n/**\n * Sent from the client to the server, to read a specific resource URI.\n */\nconst ReadResourceRequestSchema = RequestSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"resources/read\"),\n    params: BaseRequestParamsSchema.extend({\n        /**\n         * The URI of the resource to read. The URI can use any protocol; it is up to the server how to interpret it.\n         */\n        uri: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n    }),\n});\n/**\n * The server's response to a resources/read request from the client.\n */\nconst ReadResourceResultSchema = ResultSchema.extend({\n    contents: zod__WEBPACK_IMPORTED_MODULE_0__.array(zod__WEBPACK_IMPORTED_MODULE_0__.union([TextResourceContentsSchema, BlobResourceContentsSchema])),\n});\n/**\n * An optional notification from the server to the client, informing it that the list of resources it can read from has changed. This may be issued by servers without any previous subscription from the client.\n */\nconst ResourceListChangedNotificationSchema = NotificationSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"notifications/resources/list_changed\"),\n});\n/**\n * Sent from the client to request resources/updated notifications from the server whenever a particular resource changes.\n */\nconst SubscribeRequestSchema = RequestSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"resources/subscribe\"),\n    params: BaseRequestParamsSchema.extend({\n        /**\n         * The URI of the resource to subscribe to. The URI can use any protocol; it is up to the server how to interpret it.\n         */\n        uri: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n    }),\n});\n/**\n * Sent from the client to request cancellation of resources/updated notifications from the server. This should follow a previous resources/subscribe request.\n */\nconst UnsubscribeRequestSchema = RequestSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"resources/unsubscribe\"),\n    params: BaseRequestParamsSchema.extend({\n        /**\n         * The URI of the resource to unsubscribe from.\n         */\n        uri: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n    }),\n});\n/**\n * A notification from the server to the client, informing it that a resource has changed and may need to be read again. This should only be sent if the client previously sent a resources/subscribe request.\n */\nconst ResourceUpdatedNotificationSchema = NotificationSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"notifications/resources/updated\"),\n    params: BaseNotificationParamsSchema.extend({\n        /**\n         * The URI of the resource that has been updated. This might be a sub-resource of the one that the client actually subscribed to.\n         */\n        uri: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n    }),\n});\n/* Prompts */\n/**\n * Describes an argument that a prompt can accept.\n */\nconst PromptArgumentSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    /**\n     * The name of the argument.\n     */\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n    /**\n     * A human-readable description of the argument.\n     */\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.string()),\n    /**\n     * Whether this argument must be provided.\n     */\n    required: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.boolean()),\n})\n    .passthrough();\n/**\n * A prompt or prompt template that the server offers.\n */\nconst PromptSchema = BaseMetadataSchema.extend({\n    /**\n     * An optional description of what this prompt provides\n     */\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.string()),\n    /**\n     * A list of arguments to use for templating the prompt.\n     */\n    arguments: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.array(PromptArgumentSchema)),\n    /**\n     * See [MCP specification](https://github.com/modelcontextprotocol/modelcontextprotocol/blob/47339c03c143bb4ec01a26e721a1b8fe66634ebe/docs/specification/draft/basic/index.mdx#general-fields)\n     * for notes on _meta usage.\n     */\n    _meta: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.object({}).passthrough()),\n});\n/**\n * Sent from the client to request a list of prompts and prompt templates the server has.\n */\nconst ListPromptsRequestSchema = PaginatedRequestSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"prompts/list\"),\n});\n/**\n * The server's response to a prompts/list request from the client.\n */\nconst ListPromptsResultSchema = PaginatedResultSchema.extend({\n    prompts: zod__WEBPACK_IMPORTED_MODULE_0__.array(PromptSchema),\n});\n/**\n * Used by the client to get a prompt provided by the server.\n */\nconst GetPromptRequestSchema = RequestSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"prompts/get\"),\n    params: BaseRequestParamsSchema.extend({\n        /**\n         * The name of the prompt or prompt template.\n         */\n        name: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        /**\n         * Arguments to use for templating the prompt.\n         */\n        arguments: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.record(zod__WEBPACK_IMPORTED_MODULE_0__.string())),\n    }),\n});\n/**\n * Text provided to or from an LLM.\n */\nconst TextContentSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    type: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"text\"),\n    /**\n     * The text content of the message.\n     */\n    text: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n    /**\n     * See [MCP specification](https://github.com/modelcontextprotocol/modelcontextprotocol/blob/47339c03c143bb4ec01a26e721a1b8fe66634ebe/docs/specification/draft/basic/index.mdx#general-fields)\n     * for notes on _meta usage.\n     */\n    _meta: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.object({}).passthrough()),\n})\n    .passthrough();\n/**\n * An image provided to or from an LLM.\n */\nconst ImageContentSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    type: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"image\"),\n    /**\n     * The base64-encoded image data.\n     */\n    data: zod__WEBPACK_IMPORTED_MODULE_0__.string().base64(),\n    /**\n     * The MIME type of the image. Different providers may support different image types.\n     */\n    mimeType: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n    /**\n     * See [MCP specification](https://github.com/modelcontextprotocol/modelcontextprotocol/blob/47339c03c143bb4ec01a26e721a1b8fe66634ebe/docs/specification/draft/basic/index.mdx#general-fields)\n     * for notes on _meta usage.\n     */\n    _meta: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.object({}).passthrough()),\n})\n    .passthrough();\n/**\n * An Audio provided to or from an LLM.\n */\nconst AudioContentSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    type: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"audio\"),\n    /**\n     * The base64-encoded audio data.\n     */\n    data: zod__WEBPACK_IMPORTED_MODULE_0__.string().base64(),\n    /**\n     * The MIME type of the audio. Different providers may support different audio types.\n     */\n    mimeType: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n    /**\n     * See [MCP specification](https://github.com/modelcontextprotocol/modelcontextprotocol/blob/47339c03c143bb4ec01a26e721a1b8fe66634ebe/docs/specification/draft/basic/index.mdx#general-fields)\n     * for notes on _meta usage.\n     */\n    _meta: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.object({}).passthrough()),\n})\n    .passthrough();\n/**\n * The contents of a resource, embedded into a prompt or tool call result.\n */\nconst EmbeddedResourceSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    type: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"resource\"),\n    resource: zod__WEBPACK_IMPORTED_MODULE_0__.union([TextResourceContentsSchema, BlobResourceContentsSchema]),\n    /**\n     * See [MCP specification](https://github.com/modelcontextprotocol/modelcontextprotocol/blob/47339c03c143bb4ec01a26e721a1b8fe66634ebe/docs/specification/draft/basic/index.mdx#general-fields)\n     * for notes on _meta usage.\n     */\n    _meta: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.object({}).passthrough()),\n})\n    .passthrough();\n/**\n * A resource that the server is capable of reading, included in a prompt or tool call result.\n *\n * Note: resource links returned by tools are not guaranteed to appear in the results of `resources/list` requests.\n */\nconst ResourceLinkSchema = ResourceSchema.extend({\n    type: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"resource_link\"),\n});\n/**\n * A content block that can be used in prompts and tool results.\n */\nconst ContentBlockSchema = zod__WEBPACK_IMPORTED_MODULE_0__.union([\n    TextContentSchema,\n    ImageContentSchema,\n    AudioContentSchema,\n    ResourceLinkSchema,\n    EmbeddedResourceSchema,\n]);\n/**\n * Describes a message returned as part of a prompt.\n */\nconst PromptMessageSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    role: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\"user\", \"assistant\"]),\n    content: ContentBlockSchema,\n})\n    .passthrough();\n/**\n * The server's response to a prompts/get request from the client.\n */\nconst GetPromptResultSchema = ResultSchema.extend({\n    /**\n     * An optional description for the prompt.\n     */\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.string()),\n    messages: zod__WEBPACK_IMPORTED_MODULE_0__.array(PromptMessageSchema),\n});\n/**\n * An optional notification from the server to the client, informing it that the list of prompts it offers has changed. This may be issued by servers without any previous subscription from the client.\n */\nconst PromptListChangedNotificationSchema = NotificationSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"notifications/prompts/list_changed\"),\n});\n/* Tools */\n/**\n * Additional properties describing a Tool to clients.\n *\n * NOTE: all properties in ToolAnnotations are **hints**.\n * They are not guaranteed to provide a faithful description of\n * tool behavior (including descriptive properties like `title`).\n *\n * Clients should never make tool use decisions based on ToolAnnotations\n * received from untrusted servers.\n */\nconst ToolAnnotationsSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    /**\n     * A human-readable title for the tool.\n     */\n    title: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.string()),\n    /**\n     * If true, the tool does not modify its environment.\n     *\n     * Default: false\n     */\n    readOnlyHint: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.boolean()),\n    /**\n     * If true, the tool may perform destructive updates to its environment.\n     * If false, the tool performs only additive updates.\n     *\n     * (This property is meaningful only when `readOnlyHint == false`)\n     *\n     * Default: true\n     */\n    destructiveHint: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.boolean()),\n    /**\n     * If true, calling the tool repeatedly with the same arguments\n     * will have no additional effect on the its environment.\n     *\n     * (This property is meaningful only when `readOnlyHint == false`)\n     *\n     * Default: false\n     */\n    idempotentHint: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.boolean()),\n    /**\n     * If true, this tool may interact with an \"open world\" of external\n     * entities. If false, the tool's domain of interaction is closed.\n     * For example, the world of a web search tool is open, whereas that\n     * of a memory tool is not.\n     *\n     * Default: true\n     */\n    openWorldHint: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.boolean()),\n})\n    .passthrough();\n/**\n * Definition for a tool the client can call.\n */\nconst ToolSchema = BaseMetadataSchema.extend({\n    /**\n     * A human-readable description of the tool.\n     */\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.string()),\n    /**\n     * A JSON Schema object defining the expected parameters for the tool.\n     */\n    inputSchema: zod__WEBPACK_IMPORTED_MODULE_0__.object({\n        type: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"object\"),\n        properties: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.object({}).passthrough()),\n        required: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.array(zod__WEBPACK_IMPORTED_MODULE_0__.string())),\n    })\n        .passthrough(),\n    /**\n     * An optional JSON Schema object defining the structure of the tool's output returned in\n     * the structuredContent field of a CallToolResult.\n     */\n    outputSchema: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.object({\n        type: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"object\"),\n        properties: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.object({}).passthrough()),\n        required: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.array(zod__WEBPACK_IMPORTED_MODULE_0__.string())),\n    })\n        .passthrough()),\n    /**\n     * Optional additional tool information.\n     */\n    annotations: zod__WEBPACK_IMPORTED_MODULE_0__.optional(ToolAnnotationsSchema),\n    /**\n     * See [MCP specification](https://github.com/modelcontextprotocol/modelcontextprotocol/blob/47339c03c143bb4ec01a26e721a1b8fe66634ebe/docs/specification/draft/basic/index.mdx#general-fields)\n     * for notes on _meta usage.\n     */\n    _meta: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.object({}).passthrough()),\n});\n/**\n * Sent from the client to request a list of tools the server has.\n */\nconst ListToolsRequestSchema = PaginatedRequestSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"tools/list\"),\n});\n/**\n * The server's response to a tools/list request from the client.\n */\nconst ListToolsResultSchema = PaginatedResultSchema.extend({\n    tools: zod__WEBPACK_IMPORTED_MODULE_0__.array(ToolSchema),\n});\n/**\n * The server's response to a tool call.\n */\nconst CallToolResultSchema = ResultSchema.extend({\n    /**\n     * A list of content objects that represent the result of the tool call.\n     *\n     * If the Tool does not define an outputSchema, this field MUST be present in the result.\n     * For backwards compatibility, this field is always present, but it may be empty.\n     */\n    content: zod__WEBPACK_IMPORTED_MODULE_0__.array(ContentBlockSchema).default([]),\n    /**\n     * An object containing structured tool output.\n     *\n     * If the Tool defines an outputSchema, this field MUST be present in the result, and contain a JSON object that matches the schema.\n     */\n    structuredContent: zod__WEBPACK_IMPORTED_MODULE_0__.object({}).passthrough().optional(),\n    /**\n     * Whether the tool call ended in an error.\n     *\n     * If not set, this is assumed to be false (the call was successful).\n     *\n     * Any errors that originate from the tool SHOULD be reported inside the result\n     * object, with `isError` set to true, _not_ as an MCP protocol-level error\n     * response. Otherwise, the LLM would not be able to see that an error occurred\n     * and self-correct.\n     *\n     * However, any errors in _finding_ the tool, an error indicating that the\n     * server does not support tool calls, or any other exceptional conditions,\n     * should be reported as an MCP error response.\n     */\n    isError: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.boolean()),\n});\n/**\n * CallToolResultSchema extended with backwards compatibility to protocol version 2024-10-07.\n */\nconst CompatibilityCallToolResultSchema = CallToolResultSchema.or(ResultSchema.extend({\n    toolResult: zod__WEBPACK_IMPORTED_MODULE_0__.unknown(),\n}));\n/**\n * Used by the client to invoke a tool provided by the server.\n */\nconst CallToolRequestSchema = RequestSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"tools/call\"),\n    params: BaseRequestParamsSchema.extend({\n        name: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        arguments: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.record(zod__WEBPACK_IMPORTED_MODULE_0__.unknown())),\n    }),\n});\n/**\n * An optional notification from the server to the client, informing it that the list of tools it offers has changed. This may be issued by servers without any previous subscription from the client.\n */\nconst ToolListChangedNotificationSchema = NotificationSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"notifications/tools/list_changed\"),\n});\n/* Logging */\n/**\n * The severity of a log message.\n */\nconst LoggingLevelSchema = zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\n    \"debug\",\n    \"info\",\n    \"notice\",\n    \"warning\",\n    \"error\",\n    \"critical\",\n    \"alert\",\n    \"emergency\",\n]);\n/**\n * A request from the client to the server, to enable or adjust logging.\n */\nconst SetLevelRequestSchema = RequestSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"logging/setLevel\"),\n    params: BaseRequestParamsSchema.extend({\n        /**\n         * The level of logging that the client wants to receive from the server. The server should send all logs at this level and higher (i.e., more severe) to the client as notifications/logging/message.\n         */\n        level: LoggingLevelSchema,\n    }),\n});\n/**\n * Notification of a log message passed from server to client. If no logging/setLevel request has been sent from the client, the server MAY decide which messages to send automatically.\n */\nconst LoggingMessageNotificationSchema = NotificationSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"notifications/message\"),\n    params: BaseNotificationParamsSchema.extend({\n        /**\n         * The severity of this log message.\n         */\n        level: LoggingLevelSchema,\n        /**\n         * An optional name of the logger issuing this message.\n         */\n        logger: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.string()),\n        /**\n         * The data to be logged, such as a string message or an object. Any JSON serializable type is allowed here.\n         */\n        data: zod__WEBPACK_IMPORTED_MODULE_0__.unknown(),\n    }),\n});\n/* Sampling */\n/**\n * Hints to use for model selection.\n */\nconst ModelHintSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    /**\n     * A hint for a model name.\n     */\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n})\n    .passthrough();\n/**\n * The server's preferences for model selection, requested of the client during sampling.\n */\nconst ModelPreferencesSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    /**\n     * Optional hints to use for model selection.\n     */\n    hints: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.array(ModelHintSchema)),\n    /**\n     * How much to prioritize cost when selecting a model.\n     */\n    costPriority: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.number().min(0).max(1)),\n    /**\n     * How much to prioritize sampling speed (latency) when selecting a model.\n     */\n    speedPriority: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.number().min(0).max(1)),\n    /**\n     * How much to prioritize intelligence and capabilities when selecting a model.\n     */\n    intelligencePriority: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.number().min(0).max(1)),\n})\n    .passthrough();\n/**\n * Describes a message issued to or received from an LLM API.\n */\nconst SamplingMessageSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    role: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\"user\", \"assistant\"]),\n    content: zod__WEBPACK_IMPORTED_MODULE_0__.union([TextContentSchema, ImageContentSchema, AudioContentSchema]),\n})\n    .passthrough();\n/**\n * A request from the server to sample an LLM via the client. The client has full discretion over which model to select. The client should also inform the user before beginning sampling, to allow them to inspect the request (human in the loop) and decide whether to approve it.\n */\nconst CreateMessageRequestSchema = RequestSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"sampling/createMessage\"),\n    params: BaseRequestParamsSchema.extend({\n        messages: zod__WEBPACK_IMPORTED_MODULE_0__.array(SamplingMessageSchema),\n        /**\n         * An optional system prompt the server wants to use for sampling. The client MAY modify or omit this prompt.\n         */\n        systemPrompt: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.string()),\n        /**\n         * A request to include context from one or more MCP servers (including the caller), to be attached to the prompt. The client MAY ignore this request.\n         */\n        includeContext: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\"none\", \"thisServer\", \"allServers\"])),\n        temperature: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.number()),\n        /**\n         * The maximum number of tokens to sample, as requested by the server. The client MAY choose to sample fewer tokens than requested.\n         */\n        maxTokens: zod__WEBPACK_IMPORTED_MODULE_0__.number().int(),\n        stopSequences: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.array(zod__WEBPACK_IMPORTED_MODULE_0__.string())),\n        /**\n         * Optional metadata to pass through to the LLM provider. The format of this metadata is provider-specific.\n         */\n        metadata: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.object({}).passthrough()),\n        /**\n         * The server's preferences for which model to select.\n         */\n        modelPreferences: zod__WEBPACK_IMPORTED_MODULE_0__.optional(ModelPreferencesSchema),\n    }),\n});\n/**\n * The client's response to a sampling/create_message request from the server. The client should inform the user before returning the sampled message, to allow them to inspect the response (human in the loop) and decide whether to allow the server to see it.\n */\nconst CreateMessageResultSchema = ResultSchema.extend({\n    /**\n     * The name of the model that generated the message.\n     */\n    model: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n    /**\n     * The reason why sampling stopped.\n     */\n    stopReason: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\"endTurn\", \"stopSequence\", \"maxTokens\"]).or(zod__WEBPACK_IMPORTED_MODULE_0__.string())),\n    role: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\"user\", \"assistant\"]),\n    content: zod__WEBPACK_IMPORTED_MODULE_0__.discriminatedUnion(\"type\", [\n        TextContentSchema,\n        ImageContentSchema,\n        AudioContentSchema\n    ]),\n});\n/* Elicitation */\n/**\n * Primitive schema definition for boolean fields.\n */\nconst BooleanSchemaSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    type: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"boolean\"),\n    title: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.string()),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.string()),\n    default: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.boolean()),\n})\n    .passthrough();\n/**\n * Primitive schema definition for string fields.\n */\nconst StringSchemaSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    type: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"string\"),\n    title: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.string()),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.string()),\n    minLength: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.number()),\n    maxLength: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.number()),\n    format: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\"email\", \"uri\", \"date\", \"date-time\"])),\n})\n    .passthrough();\n/**\n * Primitive schema definition for number fields.\n */\nconst NumberSchemaSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    type: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\"number\", \"integer\"]),\n    title: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.string()),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.string()),\n    minimum: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.number()),\n    maximum: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.number()),\n})\n    .passthrough();\n/**\n * Primitive schema definition for enum fields.\n */\nconst EnumSchemaSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    type: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"string\"),\n    title: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.string()),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.string()),\n    enum: zod__WEBPACK_IMPORTED_MODULE_0__.array(zod__WEBPACK_IMPORTED_MODULE_0__.string()),\n    enumNames: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.array(zod__WEBPACK_IMPORTED_MODULE_0__.string())),\n})\n    .passthrough();\n/**\n * Union of all primitive schema definitions.\n */\nconst PrimitiveSchemaDefinitionSchema = zod__WEBPACK_IMPORTED_MODULE_0__.union([\n    BooleanSchemaSchema,\n    StringSchemaSchema,\n    NumberSchemaSchema,\n    EnumSchemaSchema,\n]);\n/**\n * A request from the server to elicit user input via the client.\n * The client should present the message and form fields to the user.\n */\nconst ElicitRequestSchema = RequestSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"elicitation/create\"),\n    params: BaseRequestParamsSchema.extend({\n        /**\n         * The message to present to the user.\n         */\n        message: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        /**\n         * The schema for the requested user input.\n         */\n        requestedSchema: zod__WEBPACK_IMPORTED_MODULE_0__.object({\n            type: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"object\"),\n            properties: zod__WEBPACK_IMPORTED_MODULE_0__.record(zod__WEBPACK_IMPORTED_MODULE_0__.string(), PrimitiveSchemaDefinitionSchema),\n            required: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.array(zod__WEBPACK_IMPORTED_MODULE_0__.string())),\n        })\n            .passthrough(),\n    }),\n});\n/**\n * The client's response to an elicitation/create request from the server.\n */\nconst ElicitResultSchema = ResultSchema.extend({\n    /**\n     * The user's response action.\n     */\n    action: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\"accept\", \"decline\", \"cancel\"]),\n    /**\n     * The collected user input content (only present if action is \"accept\").\n     */\n    content: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.record(zod__WEBPACK_IMPORTED_MODULE_0__.string(), zod__WEBPACK_IMPORTED_MODULE_0__.unknown())),\n});\n/* Autocomplete */\n/**\n * A reference to a resource or resource template definition.\n */\nconst ResourceTemplateReferenceSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    type: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"ref/resource\"),\n    /**\n     * The URI or URI template of the resource.\n     */\n    uri: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n})\n    .passthrough();\n/**\n * @deprecated Use ResourceTemplateReferenceSchema instead\n */\nconst ResourceReferenceSchema = ResourceTemplateReferenceSchema;\n/**\n * Identifies a prompt.\n */\nconst PromptReferenceSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    type: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"ref/prompt\"),\n    /**\n     * The name of the prompt or prompt template\n     */\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n})\n    .passthrough();\n/**\n * A request from the client to the server, to ask for completion options.\n */\nconst CompleteRequestSchema = RequestSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"completion/complete\"),\n    params: BaseRequestParamsSchema.extend({\n        ref: zod__WEBPACK_IMPORTED_MODULE_0__.union([PromptReferenceSchema, ResourceTemplateReferenceSchema]),\n        /**\n         * The argument's information\n         */\n        argument: zod__WEBPACK_IMPORTED_MODULE_0__.object({\n            /**\n             * The name of the argument\n             */\n            name: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n            /**\n             * The value of the argument to use for completion matching.\n             */\n            value: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n        })\n            .passthrough(),\n        context: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.object({\n            /**\n             * Previously-resolved variables in a URI template or prompt.\n             */\n            arguments: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.record(zod__WEBPACK_IMPORTED_MODULE_0__.string(), zod__WEBPACK_IMPORTED_MODULE_0__.string())),\n        })),\n    }),\n});\n/**\n * The server's response to a completion/complete request\n */\nconst CompleteResultSchema = ResultSchema.extend({\n    completion: zod__WEBPACK_IMPORTED_MODULE_0__.object({\n        /**\n         * An array of completion values. Must not exceed 100 items.\n         */\n        values: zod__WEBPACK_IMPORTED_MODULE_0__.array(zod__WEBPACK_IMPORTED_MODULE_0__.string()).max(100),\n        /**\n         * The total number of completion options available. This can exceed the number of values actually sent in the response.\n         */\n        total: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.number().int()),\n        /**\n         * Indicates whether there are additional completion options beyond those provided in the current response, even if the exact total is unknown.\n         */\n        hasMore: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.boolean()),\n    })\n        .passthrough(),\n});\n/* Roots */\n/**\n * Represents a root directory or file that the server can operate on.\n */\nconst RootSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    /**\n     * The URI identifying the root. This *must* start with file:// for now.\n     */\n    uri: zod__WEBPACK_IMPORTED_MODULE_0__.string().startsWith(\"file://\"),\n    /**\n     * An optional name for the root.\n     */\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.string()),\n    /**\n     * See [MCP specification](https://github.com/modelcontextprotocol/modelcontextprotocol/blob/47339c03c143bb4ec01a26e721a1b8fe66634ebe/docs/specification/draft/basic/index.mdx#general-fields)\n     * for notes on _meta usage.\n     */\n    _meta: zod__WEBPACK_IMPORTED_MODULE_0__.optional(zod__WEBPACK_IMPORTED_MODULE_0__.object({}).passthrough()),\n})\n    .passthrough();\n/**\n * Sent from the server to request a list of root URIs from the client.\n */\nconst ListRootsRequestSchema = RequestSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"roots/list\"),\n});\n/**\n * The client's response to a roots/list request from the server.\n */\nconst ListRootsResultSchema = ResultSchema.extend({\n    roots: zod__WEBPACK_IMPORTED_MODULE_0__.array(RootSchema),\n});\n/**\n * A notification from the client to the server, informing it that the list of roots has changed.\n */\nconst RootsListChangedNotificationSchema = NotificationSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"notifications/roots/list_changed\"),\n});\n/* Client messages */\nconst ClientRequestSchema = zod__WEBPACK_IMPORTED_MODULE_0__.union([\n    PingRequestSchema,\n    InitializeRequestSchema,\n    CompleteRequestSchema,\n    SetLevelRequestSchema,\n    GetPromptRequestSchema,\n    ListPromptsRequestSchema,\n    ListResourcesRequestSchema,\n    ListResourceTemplatesRequestSchema,\n    ReadResourceRequestSchema,\n    SubscribeRequestSchema,\n    UnsubscribeRequestSchema,\n    CallToolRequestSchema,\n    ListToolsRequestSchema,\n]);\nconst ClientNotificationSchema = zod__WEBPACK_IMPORTED_MODULE_0__.union([\n    CancelledNotificationSchema,\n    ProgressNotificationSchema,\n    InitializedNotificationSchema,\n    RootsListChangedNotificationSchema,\n]);\nconst ClientResultSchema = zod__WEBPACK_IMPORTED_MODULE_0__.union([\n    EmptyResultSchema,\n    CreateMessageResultSchema,\n    ElicitResultSchema,\n    ListRootsResultSchema,\n]);\n/* Server messages */\nconst ServerRequestSchema = zod__WEBPACK_IMPORTED_MODULE_0__.union([\n    PingRequestSchema,\n    CreateMessageRequestSchema,\n    ElicitRequestSchema,\n    ListRootsRequestSchema,\n]);\nconst ServerNotificationSchema = zod__WEBPACK_IMPORTED_MODULE_0__.union([\n    CancelledNotificationSchema,\n    ProgressNotificationSchema,\n    LoggingMessageNotificationSchema,\n    ResourceUpdatedNotificationSchema,\n    ResourceListChangedNotificationSchema,\n    ToolListChangedNotificationSchema,\n    PromptListChangedNotificationSchema,\n]);\nconst ServerResultSchema = zod__WEBPACK_IMPORTED_MODULE_0__.union([\n    EmptyResultSchema,\n    InitializeResultSchema,\n    CompleteResultSchema,\n    GetPromptResultSchema,\n    ListPromptsResultSchema,\n    ListResourcesResultSchema,\n    ListResourceTemplatesResultSchema,\n    ReadResourceResultSchema,\n    CallToolResultSchema,\n    ListToolsResultSchema,\n]);\nclass McpError extends Error {\n    constructor(code, message, data) {\n        super(`MCP error ${code}: ${message}`);\n        this.code = code;\n        this.data = data;\n        this.name = \"McpError\";\n    }\n}\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/types.js\n");

/***/ })

};
;