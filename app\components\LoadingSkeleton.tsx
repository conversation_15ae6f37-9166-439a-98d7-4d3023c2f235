'use client';

import React from 'react';
import { Box, Skeleton } from '@mui/material';
import { motion } from 'framer-motion';

interface LoadingSkeletonProps {
  type: 'conversation' | 'message' | 'model';
  count?: number;
}

export default function LoadingSkeleton({ type, count = 1 }: LoadingSkeletonProps) {
  const renderConversationSkeleton = () => (
    <Box sx={{ p: 2 }}>
      <Skeleton
        variant="rectangular"
        height={60}
        sx={{
          borderRadius: 2,
          bgcolor: 'rgba(255, 255, 255, 0.05)',
          mb: 1,
        }}
      />
      <Skeleton
        variant="text"
        width="80%"
        sx={{
          bgcolor: 'rgba(255, 255, 255, 0.05)',
        }}
      />
      <Skeleton
        variant="text"
        width="60%"
        sx={{
          bgcolor: 'rgba(255, 255, 255, 0.05)',
        }}
      />
    </Box>
  );

  const renderMessageSkeleton = () => (
    <Box sx={{ mb: 3, display: 'flex', justifyContent: 'flex-start' }}>
      <Box sx={{ maxWidth: '80%' }}>
        <Skeleton
          variant="rectangular"
          height={80}
          sx={{
            borderRadius: 3,
            bgcolor: 'rgba(255, 255, 255, 0.05)',
            mb: 1,
          }}
        />
        <Skeleton
          variant="text"
          width="40%"
          sx={{
            bgcolor: 'rgba(255, 255, 255, 0.05)',
          }}
        />
      </Box>
    </Box>
  );

  const renderModelSkeleton = () => (
    <Box sx={{ p: 2 }}>
      <Skeleton
        variant="rectangular"
        height={120}
        sx={{
          borderRadius: 3,
          bgcolor: 'rgba(255, 255, 255, 0.05)',
          mb: 2,
        }}
      />
      <Skeleton
        variant="text"
        width="70%"
        sx={{
          bgcolor: 'rgba(255, 255, 255, 0.05)',
          mb: 1,
        }}
      />
      <Skeleton
        variant="text"
        width="50%"
        sx={{
          bgcolor: 'rgba(255, 255, 255, 0.05)',
        }}
      />
    </Box>
  );

  const renderSkeleton = () => {
    switch (type) {
      case 'conversation':
        return renderConversationSkeleton();
      case 'message':
        return renderMessageSkeleton();
      case 'model':
        return renderModelSkeleton();
      default:
        return null;
    }
  };

  return (
    <>
      {Array.from({ length: count }).map((_, index) => (
        <motion.div
          key={index}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1, duration: 0.3 }}
        >
          {renderSkeleton()}
        </motion.div>
      ))}
    </>
  );
}
