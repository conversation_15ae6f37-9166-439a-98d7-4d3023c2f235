{"version": 3, "file": "audio.js", "sourceRoot": "", "sources": ["../src/helpers/audio.ts"], "names": [], "mappings": ";;;AAAA,iDAAqC;AACrC,2DAA2C;AAC3C,6CAAuC;AACvC,+CAAkD;AAGlD,MAAM,mBAAmB,GAAG,KAAK,CAAC;AAClC,MAAM,gBAAgB,GAAG,CAAC,CAAC;AAE3B,MAAM,MAAM,GAAG,OAAO,CAAC,uBAAQ,EAAE,IAAI,CAAC,CAAC;AAEvC,MAAM,kBAAkB,GAAoC;IAC1D,KAAK,EAAE,OAAO;IACd,MAAM,EAAE,cAAc;IACtB,KAAK,EAAE,MAAM;IACb,GAAG,EAAE,MAAM;IACX,OAAO,EAAE,MAAM;IACf,OAAO,EAAE,MAAM;IACf,KAAK,EAAE,MAAM;IACb,KAAK,EAAE,MAAM;IACb,MAAM,EAAE,MAAM;IACd,OAAO,EAAE,MAAM;IACf,MAAM,EAAE,OAAO;CAChB,CAAC;AAEF,SAAS,UAAU,CAAC,MAA+C;IACjE,OAAO,OAAQ,MAAc,CAAC,IAAI,KAAK,WAAW,CAAC;AACrD,CAAC;AAED,SAAS,MAAM,CAAC,MAA+C;IAC7D,OAAO,MAAM,YAAY,oBAAI,CAAC;AAChC,CAAC;AAED,KAAK,UAAU,eAAe,CAAC,MAA+C;IAC5E,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACrC,IAAI;YACF,MAAM,MAAM,GAAG,IAAA,0BAAK,EAAC,QAAQ,EAAE,CAAC,WAAW,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;YAEzE,IAAI,UAAU,CAAC,MAAM,CAAC,EAAE;gBACtB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aAChC;iBAAM,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE;gBACzB,sBAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aACnD;iBAAM;gBACL,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aAC3B;YAED,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,IAAY,EAAE,EAAE;gBAClC,IAAI,IAAI,KAAK,CAAC,EAAE;oBACd,MAAM,CAAC,IAAI,KAAK,CAAC,mCAAmC,IAAI,EAAE,CAAC,CAAC,CAAC;iBAC9D;gBACD,OAAO,EAAE,CAAC;YACZ,CAAC,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,KAAK,CAAC,CAAC;SACf;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAEM,KAAK,UAAU,SAAS,CAAC,KAA8C;IAC5E,IAAI,MAAM,EAAE;QACV,OAAO,eAAe,CAAC,KAAK,CAAC,CAAC;KAC/B;IAED,MAAM,IAAI,KAAK,CACb,sGAAsG,CACvG,CAAC;AACJ,CAAC;AARD,8BAQC;AAQD,SAAS,iBAAiB,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,KAAyB,EAAE;IAC7E,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACrC,MAAM,IAAI,GAAU,EAAE,CAAC;QACvB,MAAM,QAAQ,GAAG,kBAAkB,CAAC,uBAAQ,CAAC,CAAC;QAC9C,IAAI;YACF,MAAM,MAAM,GAAG,IAAA,0BAAK,EAClB,QAAQ,EACR;gBACE,IAAI;gBACJ,QAAQ;gBACR,IAAI;gBACJ,IAAI,MAAM,IAAI,CAAC,EAAE;gBACjB,KAAK;gBACL,mBAAmB,CAAC,QAAQ,EAAE;gBAC9B,KAAK;gBACL,gBAAgB,CAAC,QAAQ,EAAE;gBAC3B,IAAI;gBACJ,KAAK;gBACL,QAAQ;aACT,EACD;gBACE,KAAK,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC;aAClC,CACF,CAAC;YAEF,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,EAAE;gBACjC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC;YAEH,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;gBAC3B,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBACrB,MAAM,CAAC,KAAK,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC;YAEH,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE;gBAC1B,UAAU,EAAE,CAAC;YACf,CAAC,CAAC,CAAC;YAEH,SAAS,UAAU;gBACjB,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBACxC,MAAM,SAAS,GAAG,IAAI,oBAAI,CAAC,CAAC,WAAW,CAAC,EAAE,WAAW,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;gBAC9E,OAAO,CAAC,SAAS,CAAC,CAAC;YACrB,CAAC;YAED,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,GAAG,CAAC,EAAE;gBAC9C,MAAM,cAAc,GAAG,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;gBACpD,cAAc,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;oBAC5C,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBACzB,CAAC,CAAC,CAAC;aACJ;YAED,IAAI,MAAM,EAAE;gBACV,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;oBACpC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBACzB,CAAC,CAAC,CAAC;aACJ;SACF;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,KAAK,CAAC,CAAC;SACf;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAEM,KAAK,UAAU,WAAW,CAAC,UAA8B,EAAE;IAChE,IAAI,MAAM,EAAE;QACV,OAAO,iBAAiB,CAAC,OAAO,CAAC,CAAC;KACnC;IAED,MAAM,IAAI,KAAK,CACb,oGAAoG,CACrG,CAAC;AACJ,CAAC;AARD,kCAQC"}