'use client';

import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Container,
  Typography,
  Alert,
  Skeleton,
  Fade,
  CircularProgress,
} from '@mui/material';
import { v4 as uuidv4 } from 'uuid';
import { motion, AnimatePresence } from 'framer-motion';
import MessageBubble from './MessageBubble';
import InputBar from './InputBar';
import TypingIndicator from '../TypingIndicator';
import { ChatMessage, FileAttachment, Tool, ToolCall } from '@/types';

interface Conversation {
  id: string;
  title: string;
  lastMessage: string;
  timestamp: Date;
  messageCount: number;
  messages: ChatMessage[];
}

interface ChatWindowProps {
  selectedModel: string | null;
  availableTools: Tool[];
  conversation: Conversation | null;
  onMessageSent: (message: ChatMessage) => void;
  modelCapabilities?: any;
}

export default function ChatWindow({
  selectedModel,
  availableTools,
  conversation,
  onMessageSent,
  modelCapabilities
}: ChatWindowProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const messages = conversation?.messages || [];

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = async (content: string, attachments?: FileAttachment[]) => {
    if (!selectedModel) {
      setError('Please select a model first');
      return;
    }

    // Create user message
    const userMessage: ChatMessage = {
      id: uuidv4(),
      role: 'user',
      content,
      timestamp: new Date(),
      attachments,
    };

    onMessageSent(userMessage);
    setLoading(true);
    setError(null);

    try {
      // Check if message has vision content
      const hasVisionContent = attachments?.some(att => att.type === 'image');

      // Validate vision capability
      if (hasVisionContent && !modelCapabilities?.supportsVision) {
        setError(`Model ${selectedModel} does not support vision/image processing. Please select a vision-capable model like llava or qwen2-vl.`);
        setLoading(false);
        return;
      }

      // Prepare messages for API
      const conversationMessages = [...messages, userMessage];

      // Only include tools if model supports them
      const toolsToSend = modelCapabilities?.supportsTools ? availableTools : [];

      console.log('ChatWindow - Available tools:', availableTools);
      console.log('ChatWindow - Model capabilities:', modelCapabilities);
      console.log('ChatWindow - Tools to send:', toolsToSend);

      // Send to Ollama API
      const response = await fetch('/api/ollama', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: selectedModel,
          messages: conversationMessages.map(msg => ({
            role: msg.role,
            content: msg.content,
            ...(msg.toolCall && { tool_calls: [msg.toolCall] }),
          })),
          tools: toolsToSend,
          hasVisionContent,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to get response from Ollama');
      }

      const data = await response.json();
      const assistantMessage = data.message;

      console.log('Received assistant message:', assistantMessage);
      console.log('Model capabilities:', modelCapabilities);

      // Create assistant message
      const newAssistantMessage: ChatMessage = {
        id: uuidv4(),
        role: 'assistant',
        content: assistantMessage.content,
        timestamp: new Date(),
      };

      // Check if there are tool calls (only if model supports tools)
      if (modelCapabilities?.supportsTools &&
          assistantMessage.tool_calls &&
          assistantMessage.tool_calls.length > 0) {
        console.log('Tool calls detected:', assistantMessage.tool_calls);
        const toolCall = assistantMessage.tool_calls[0];

        // Add an ID to the tool call if it doesn't have one
        if (!toolCall.id) {
          toolCall.id = uuidv4();
        }

        newAssistantMessage.toolCall = toolCall;

        // Add the assistant message with tool call
        onMessageSent(newAssistantMessage);

        // Execute the tool call
        await executeToolCall(toolCall, [...messages, userMessage, newAssistantMessage]);
      } else {
        // Add the assistant message
        onMessageSent(newAssistantMessage);
      }
    } catch (err) {
      console.error('Error sending message:', err);
      setError('Failed to send message. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const executeToolCall = async (toolCall: ToolCall, currentMessages: ChatMessage[]) => {
    try {
      console.log('Executing tool call:', toolCall);

      // Parse tool arguments
      let toolArgs;
      try {
        toolArgs = typeof toolCall.function.arguments === 'string'
          ? JSON.parse(toolCall.function.arguments)
          : toolCall.function.arguments;
      } catch (parseError) {
        console.error('Failed to parse tool arguments:', parseError);
        toolArgs = toolCall.function.arguments;
      }

      console.log('Tool arguments:', toolArgs);

      // Determine server name based on tool name
      let serverName = 'exa'; // default
      if (toolCall.function.name === 'get_weather') {
        serverName = 'weather';
      } else if (toolCall.function.name === 'search_web') {
        serverName = 'exa';
      }

      console.log(`Using server: ${serverName} for tool: ${toolCall.function.name}`);

      // Execute tool via MCP API
      const response = await fetch('/api/mcp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'execute_tool',
          serverName: serverName,
          toolName: toolCall.function.name,
          arguments: toolArgs,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to execute tool');
      }

      const toolResult = await response.json();

      // Create tool result message
      const toolResultMessage: ChatMessage = {
        id: uuidv4(),
        role: 'tool',
        content: JSON.stringify(toolResult.result, null, 2),
        timestamp: new Date(),
        toolResult: {
          toolCallId: toolCall.id,
          result: toolResult.result,
        },
      };

      onMessageSent(toolResultMessage);

      // Send the tool result back to Ollama for final response
      const finalResponse = await fetch('/api/ollama', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: selectedModel,
          messages: [...currentMessages, toolResultMessage].map(msg => ({
            role: msg.role,
            content: msg.content,
            ...(msg.toolCall && { tool_calls: [msg.toolCall] }),
          })),
          tools: availableTools,
        }),
      });

      if (finalResponse.ok) {
        const finalData = await finalResponse.json();
        const finalMessage: ChatMessage = {
          id: uuidv4(),
          role: 'assistant',
          content: finalData.message.content,
          timestamp: new Date(),
        };

        onMessageSent(finalMessage);
      }
    } catch (err) {
      console.error('Error executing tool call:', err);
      
      // Add error message
      const errorMessage: ChatMessage = {
        id: uuidv4(),
        role: 'assistant',
        content: 'Sorry, I encountered an error while using the tool. Please try again.',
        timestamp: new Date(),
      };

      onMessageSent(errorMessage);
    }
  };

  return (
    <Box display="flex" flexDirection="column" height="100%">
      {/* Error Alert */}
      <AnimatePresence>
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <Box sx={{ p: 2 }}>
              <Alert
                severity="error"
                onClose={() => setError(null)}
                sx={{
                  borderRadius: 2,
                  backdropFilter: 'blur(20px)',
                  background: 'rgba(255, 68, 68, 0.1)',
                  border: '1px solid rgba(255, 68, 68, 0.3)',
                  color: '#ff4444',
                  '& .MuiAlert-icon': {
                    color: '#ff4444',
                  },
                }}
              >
                {error}
              </Alert>
            </Box>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Messages Area */}
      <Box flex={1} overflow="auto" sx={{ px: 3, py: 2 }}>
        {messages.length === 0 ? (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            style={{ height: '100%' }}
          >
            <Box
              display="flex"
              flexDirection="column"
              alignItems="center"
              justifyContent="center"
              height="100%"
              textAlign="center"
            >
              <Typography
                variant="h6"
                sx={{
                  color: 'rgba(255, 255, 255, 0.8)',
                  mb: 1,
                  fontWeight: 500,
                }}
              >
                {conversation ? 'Start chatting' : 'Select a conversation'}
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  color: 'rgba(255, 255, 255, 0.6)',
                  maxWidth: 400,
                }}
              >
                {conversation
                  ? 'Type a message to begin the conversation with your AI assistant'
                  : 'Choose a conversation from the sidebar or create a new one to get started'
                }
              </Typography>
            </Box>
          </motion.div>
        ) : (
          <AnimatePresence>
            {messages.map((message, index) => (
              <motion.div
                key={message.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1, duration: 0.3 }}
              >
                <MessageBubble
                  message={message}
                  isUser={message.role === 'user'}
                />
              </motion.div>
            ))}
          </AnimatePresence>
        )}

        {/* Loading indicator */}
        <AnimatePresence>
          {loading && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <TypingIndicator />
            </motion.div>
          )}
        </AnimatePresence>

        <div ref={messagesEndRef} />
      </Box>

      {/* Input Area */}
      <Box sx={{ p: 3, borderTop: '1px solid rgba(255, 255, 255, 0.1)' }}>
        <InputBar
          onSendMessage={handleSendMessage}
          disabled={!selectedModel || !conversation}
          loading={loading}
          modelCapabilities={modelCapabilities}
        />
      </Box>
    </Box>
  );
}
