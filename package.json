{"name": "ollama-mcp-chat", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.1", "@mui/material": "^5.15.1", "@mui/system": "^5.15.1", "axios": "^1.6.2", "framer-motion": "^10.18.0", "multer": "^1.4.5-lts.1", "next": "14.0.4", "openai": "^4.20.1", "pdf-parse": "^1.1.1", "react": "^18.2.0", "react-dom": "^18.2.0", "uuid": "^9.0.1"}, "devDependencies": {"@types/multer": "^1.4.11", "@types/node": "^20.10.5", "@types/pdf-parse": "^1.1.4", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/uuid": "^9.0.7", "eslint": "^8.56.0", "eslint-config-next": "14.0.4", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}}