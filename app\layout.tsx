import type { Metada<PERSON> } from 'next';
import ThemeProvider from './components/ThemeProvider';
import AnimatedBackground from './components/AnimatedBackground';

export const metadata: Metadata = {
  title: 'Ollama MCP Chat',
  description: 'A dynamic chat interface for Ollama LLM with integrated Smithery.ai MCP tool servers',
  keywords: ['ollama', 'mcp', 'chat', 'ai', 'llm', 'smithery'],
  authors: [{ name: 'Ollama MCP Chat' }],
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="" />
        <link
          href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
          rel="stylesheet"
        />
      </head>
      <body>
        <ThemeProvider>
          <AnimatedBackground />
          {children}
        </ThemeProvider>
      </body>
    </html>
  );
}
