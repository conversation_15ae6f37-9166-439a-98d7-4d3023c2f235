# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=पिछला पृष्ठ
previous_label=पिछला
next.title=अगला पृष्ठ
next_label=आगे

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=पृष्ठ:
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages={{pagesCount}} का
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} of {{pagesCount}})

zoom_out.title=\u0020छोटा करें
zoom_out_label=\u0020छोटा करें
zoom_in.title=बड़ा करें
zoom_in_label=बड़ा करें
zoom.title=बड़ा-छोटा करें
presentation_mode.title=प्रस्तुति अवस्था में जाएँ
presentation_mode_label=\u0020प्रस्तुति अवस्था
open_file.title=फ़ाइल खोलें
open_file_label=\u0020खोलें
print.title=छापें
print_label=\u0020छापें
download.title=डाउनलोड
download_label=डाउनलोड
bookmark.title=मौजूदा दृश्य (नए विंडो में नक़ल लें या खोलें)
bookmark_label=\u0020मौजूदा दृश्य

# Secondary toolbar and context menu
tools.title=औज़ार
tools_label=औज़ार
first_page.title=प्रथम पृष्ठ पर जाएँ
first_page.label=\u0020प्रथम पृष्ठ पर जाएँ
first_page_label=प्रथम पृष्ठ पर जाएँ
last_page.title=अंतिम पृष्ठ पर जाएँ
last_page.label=\u0020अंतिम पृष्ठ पर जाएँ
last_page_label=\u0020अंतिम पृष्ठ पर जाएँ
page_rotate_cw.title=घड़ी की दिशा में घुमाएँ
page_rotate_cw.label=घड़ी की दिशा में घुमाएँ
page_rotate_cw_label=घड़ी की दिशा में घुमाएँ
page_rotate_ccw.title=घड़ी की दिशा से उल्टा घुमाएँ
page_rotate_ccw.label=घड़ी की दिशा से उल्टा घुमाएँ
page_rotate_ccw_label=\u0020घड़ी की दिशा से उल्टा घुमाएँ

cursor_text_select_tool.title=पाठ चयन उपकरण सक्षम करें
cursor_text_select_tool_label=पाठ चयन उपकरण
cursor_hand_tool.title=हस्त उपकरण सक्षम करें
cursor_hand_tool_label=हस्त उपकरण

# Document properties dialog box
document_properties.title=दस्तावेज़ विशेषता...
document_properties_label=दस्तावेज़ विशेषता...
document_properties_file_name=फ़ाइल नाम:
document_properties_file_size=फाइल आकारः
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} KB ({{size_b}} bytes)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} MB ({{size_b}} bytes)
document_properties_title=शीर्षक:
document_properties_author=लेखकः
document_properties_subject=विषय:
document_properties_keywords=कुंजी-शब्द:
document_properties_creation_date=निर्माण दिनांक:
document_properties_modification_date=संशोधन दिनांक:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=निर्माता:
document_properties_producer=PDF उत्पादक:
document_properties_version=PDF संस्करण:
document_properties_page_count=पृष्ठ गिनती:
document_properties_close=बंद करें

print_progress_message=छपाई के लिए दस्तावेज़ को तैयार किया जा रहा है...
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=रद्द करें

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=\u0020स्लाइडर टॉगल करें
toggle_sidebar_label=स्लाइडर टॉगल करें
document_outline.title=दस्तावेज़ की रूपरेखा दिखाइए (सारी वस्तुओं को फलने अथवा समेटने के लिए दो बार क्लिक करें)
document_outline_label=दस्तावेज़ आउटलाइन
attachments.title=संलग्नक दिखायें
attachments_label=संलग्नक
thumbs.title=लघुछवियाँ दिखाएँ
thumbs_label=लघु छवि
findbar.title=\u0020दस्तावेज़ में ढूँढ़ें
findbar_label=ढूँढें

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=पृष्ठ {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=पृष्ठ {{page}} की लघु-छवि

# Find panel button title and messages
find_input.title=ढूँढें
find_input.placeholder=दस्तावेज़ में खोजें...
find_previous.title=वाक्यांश की पिछली उपस्थिति ढूँढ़ें
find_previous_label=पिछला
find_next.title=वाक्यांश की अगली उपस्थिति ढूँढ़ें
find_next_label=आगे
find_highlight=\u0020सभी आलोकित करें
find_match_case_label=मिलान स्थिति
find_reached_top=पृष्ठ के ऊपर पहुंच गया, नीचे से जारी रखें
find_reached_bottom=पृष्ठ के नीचे में जा पहुँचा, ऊपर से जारी
find_not_found=वाक्यांश नहीं मिला

# Error panel labels
error_more_info=अधिक सूचना
error_less_info=कम सूचना
error_close=बंद करें
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (build: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=\u0020संदेश: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=स्टैक: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=फ़ाइल: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=पंक्ति: {{line}}
rendering_error=पृष्ठ रेंडरिंग के दौरान त्रुटि आई.

# Predefined zoom values
page_scale_width=\u0020पृष्ठ चौड़ाई
page_scale_fit=पृष्ठ फिट
page_scale_auto=स्वचालित जूम
page_scale_actual=वास्तविक आकार
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error_indicator=त्रुटि
loading_error=PDF लोड करते समय एक त्रुटि हुई.
invalid_file_error=अमान्य या भ्रष्ट PDF फ़ाइल.
missing_file_error=\u0020अनुपस्थित PDF फ़ाइल.
unexpected_response_error=अप्रत्याशित सर्वर प्रतिक्रिया.

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 – Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=\u0020[{{type}} Annotation]
password_label=इस PDF फ़ाइल को खोलने के लिए कृपया कूटशब्द भरें.
password_invalid=अवैध कूटशब्द, कृपया फिर कोशिश करें.
password_ok=OK
password_cancel=रद्द करें

printing_not_supported=चेतावनी: इस ब्राउज़र पर छपाई पूरी तरह से समर्थित नहीं है.
printing_not_ready=चेतावनी: PDF छपाई के लिए पूरी तरह से लोड नहीं है.
web_fonts_disabled=वेब फॉन्ट्स निष्क्रिय हैं: अंतःस्थापित PDF फॉन्टस के उपयोग में असमर्थ.
document_colors_not_allowed=PDF दस्तावेज़ उनके अपने रंग को उपयोग करने के लिए अनुमति प्राप्त नहीं है: "पृष्ठों को उनके अपने रंग को चुनने के लिए स्वीकृति दें" कि वह उस ब्राउज़र में निष्क्रिय है.
