'use client';

import React, { useState, useRef, useCallback } from 'react';
import {
  Box,
  TextField,
  IconButton,
  Button,
  Paper,
  Chip,
  CircularProgress,
  Tooltip,
  Typography,
  Fade,
  Zoom,
} from '@mui/material';
import {
  Send as SendIcon,
  AttachFile as AttachFileIcon,
  Image as ImageIcon,
  Close as CloseIcon,
  CloudUpload as CloudUploadIcon,
  Description as DocumentIcon,
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { FileAttachment } from '@/types';

interface InputBarProps {
  onSendMessage: (content: string, attachments?: FileAttachment[]) => void;
  disabled?: boolean;
  loading?: boolean;
  modelCapabilities?: any;
}

export default function InputBar({ onSendMessage, disabled, loading, modelCapabilities }: InputBarProps) {
  const [message, setMessage] = useState('');
  const [attachments, setAttachments] = useState<FileAttachment[]>([]);
  const [uploading, setUploading] = useState(false);
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleSend = () => {
    if (message.trim() || attachments.length > 0) {
      onSendMessage(message.trim(), attachments);
      setMessage('');
      setAttachments([]);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const processFiles = async (files: File[]) => {
    if (files.length === 0) return;

    // Check for image files and vision capability
    const imageFiles = files.filter(file => file.type.startsWith('image/'));
    if (imageFiles.length > 0 && !modelCapabilities?.supportsVision) {
      alert('The selected model does not support vision/image processing. Please select a vision-capable model like llava or qwen2-vl to upload images.');
      return;
    }

    setUploading(true);

    try {
      const uploadPromises = files.map(async (file) => {
        const formData = new FormData();
        formData.append('file', file);

        const response = await fetch('/api/upload', {
          method: 'POST',
          body: formData,
        });

        if (!response.ok) {
          throw new Error(`Failed to upload ${file.name}`);
        }

        const data = await response.json();
        return data.file as FileAttachment;
      });

      const uploadedFiles = await Promise.all(uploadPromises);
      setAttachments(prev => [...prev, ...uploadedFiles]);
    } catch (error) {
      console.error('Error uploading files:', error);
      // TODO: Show error toast
    } finally {
      setUploading(false);
    }
  };

  const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    await processFiles(files);

    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback(async (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    const files = Array.from(e.dataTransfer.files);
    await processFiles(files);
  }, [modelCapabilities]);

  const removeAttachment = (attachmentId: string) => {
    setAttachments(prev => prev.filter(att => att.id !== attachmentId));
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Box
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
      sx={{
        position: 'relative',
        transition: 'all 0.3s ease',
        ...(isDragOver && {
          '&::before': {
            content: '""',
            position: 'absolute',
            top: -10,
            left: -10,
            right: -10,
            bottom: -10,
            border: '2px dashed rgba(255, 255, 255, 0.5)',
            borderRadius: 2,
            background: 'rgba(255, 255, 255, 0.05)',
            zIndex: 1,
          },
        }),
      }}
    >
      {/* Drag overlay */}
      <AnimatePresence>
        {isDragOver && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            style={{
              position: 'absolute',
              top: -10,
              left: -10,
              right: -10,
              bottom: -10,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              background: 'rgba(255, 255, 255, 0.1)',
              borderRadius: 8,
              zIndex: 2,
            }}
          >
            <Box textAlign="center" color="white">
              <CloudUploadIcon sx={{ fontSize: 48, mb: 1 }} />
              <Typography variant="h6">Drop files here</Typography>
            </Box>
          </motion.div>
        )}
      </AnimatePresence>

      {/* File attachments preview */}
      <AnimatePresence>
        {attachments.length > 0 && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Box mb={2} display="flex" flexWrap="wrap" gap={1}>
              {attachments.map((attachment, index) => (
                <motion.div
                  key={attachment.id}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Chip
                    icon={attachment.type === 'image' ? <ImageIcon /> : <DocumentIcon />}
                    label={`${attachment.name} (${formatFileSize(attachment.size)})`}
                    onDelete={() => removeAttachment(attachment.id)}
                    deleteIcon={<CloseIcon />}
                    size="small"
                    sx={{
                      backdropFilter: 'blur(20px)',
                      background: 'rgba(255, 255, 255, 0.1)',
                      border: '1px solid rgba(255, 255, 255, 0.2)',
                      color: 'white',
                      '& .MuiChip-deleteIcon': {
                        color: 'rgba(255, 255, 255, 0.7)',
                        '&:hover': {
                          color: '#ff4444',
                        },
                      },
                    }}
                  />
                </motion.div>
              ))}
            </Box>
          </motion.div>
        )}
      </AnimatePresence>

      <Box display="flex" alignItems="flex-end" gap={2}>
        {/* File upload button */}
        <Tooltip title={
          !modelCapabilities?.supportsVision
            ? "Image uploads require a vision-capable model"
            : "Attach files"
        }>
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <IconButton
              onClick={() => fileInputRef.current?.click()}
              disabled={disabled || uploading}
              sx={{
                backdropFilter: 'blur(20px)',
                background: 'rgba(255, 255, 255, 0.05)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                color: 'white',
                transition: 'all 0.3s ease',
                '&:hover': {
                  background: 'rgba(255, 255, 255, 0.1)',
                  borderColor: 'rgba(255, 255, 255, 0.3)',
                  transform: 'translateY(-1px)',
                },
                '&:disabled': {
                  opacity: 0.5,
                },
              }}
            >
              <AnimatePresence mode="wait">
                {uploading ? (
                  <motion.div
                    key="loading"
                    initial={{ opacity: 0, rotate: 0 }}
                    animate={{ opacity: 1, rotate: 360 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <CircularProgress size={20} sx={{ color: 'white' }} />
                  </motion.div>
                ) : (
                  <motion.div
                    key="attach"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.8 }}
                    transition={{ duration: 0.2 }}
                  >
                    <AttachFileIcon />
                  </motion.div>
                )}
              </AnimatePresence>
            </IconButton>
          </motion.div>
        </Tooltip>

        {/* Hidden file input */}
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept={
            modelCapabilities?.supportsVision
              ? "image/*,.pdf,.txt,.md,.doc,.docx"
              : ".pdf,.txt,.md,.doc,.docx"
          }
          onChange={handleFileSelect}
          style={{ display: 'none' }}
        />

        {/* Message input */}
        <TextField
          fullWidth
          multiline
          maxRows={4}
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder="Type your message..."
          variant="outlined"
          disabled={disabled}
          sx={{
            '& .MuiOutlinedInput-root': {
              backdropFilter: 'blur(20px)',
              background: 'rgba(255, 255, 255, 0.05)',
              border: '1px solid rgba(255, 255, 255, 0.1)',
              borderRadius: 3,
              color: 'white',
              transition: 'all 0.3s ease',
              '& fieldset': {
                border: 'none',
              },
              '&:hover': {
                background: 'rgba(255, 255, 255, 0.08)',
                borderColor: 'rgba(255, 255, 255, 0.2)',
              },
              '&.Mui-focused': {
                background: 'rgba(255, 255, 255, 0.1)',
                borderColor: 'rgba(255, 255, 255, 0.3)',
                boxShadow: '0 0 0 2px rgba(255, 255, 255, 0.1)',
              },
            },
            '& .MuiInputBase-input': {
              color: 'white',
              '&::placeholder': {
                color: 'rgba(255, 255, 255, 0.5)',
                opacity: 1,
              },
            },
          }}
        />

        {/* Send button */}
        <Tooltip title="Send message">
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Button
              variant="contained"
              onClick={handleSend}
              disabled={disabled || loading || (!message.trim() && attachments.length === 0)}
              sx={{
                minWidth: 48,
                height: 48,
                borderRadius: 3,
                backdropFilter: 'blur(20px)',
                background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1))',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                color: 'white',
                transition: 'all 0.3s ease',
                '&:hover': {
                  background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.2))',
                  borderColor: 'rgba(255, 255, 255, 0.4)',
                  transform: 'translateY(-1px)',
                  boxShadow: '0 4px 20px rgba(255, 255, 255, 0.1)',
                },
                '&:disabled': {
                  opacity: 0.5,
                  background: 'rgba(255, 255, 255, 0.05)',
                },
              }}
            >
              <AnimatePresence mode="wait">
                {loading ? (
                  <motion.div
                    key="loading"
                    initial={{ opacity: 0, rotate: 0 }}
                    animate={{ opacity: 1, rotate: 360 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <CircularProgress size={20} sx={{ color: 'white' }} />
                  </motion.div>
                ) : (
                  <motion.div
                    key="send"
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 10 }}
                    transition={{ duration: 0.2 }}
                  >
                    <SendIcon />
                  </motion.div>
                )}
              </AnimatePresence>
            </Button>
          </motion.div>
        </Tooltip>
      </Box>
    </Box>
  );
}
