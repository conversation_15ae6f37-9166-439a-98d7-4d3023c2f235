"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/components/ChatWindow/ChatWindow.tsx":
/*!**************************************************!*\
  !*** ./app/components/ChatWindow/ChatWindow.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatWindow)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _MessageBubble__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MessageBubble */ \"(app-pages-browser)/./app/components/ChatWindow/MessageBubble.tsx\");\n/* harmony import */ var _InputBar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./InputBar */ \"(app-pages-browser)/./app/components/ChatWindow/InputBar.tsx\");\n/* harmony import */ var _TypingIndicator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../TypingIndicator */ \"(app-pages-browser)/./app/components/TypingIndicator.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ChatWindow(param) {\n    let { selectedModel, availableTools, conversation, onMessageSent, modelCapabilities } = param;\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const theme = useTheme();\n    const messages = (conversation === null || conversation === void 0 ? void 0 : conversation.messages) || [];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatWindow.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ChatWindow.useEffect\"], [\n        messages\n    ]);\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: 'smooth'\n        });\n    };\n    const handleSendMessage = async (content, attachments)=>{\n        if (!selectedModel) {\n            setError('Please select a model first');\n            return;\n        }\n        // Create user message\n        const userMessage = {\n            id: (0,uuid__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(),\n            role: 'user',\n            content,\n            timestamp: new Date(),\n            attachments\n        };\n        onMessageSent(userMessage);\n        setLoading(true);\n        setError(null);\n        try {\n            // Check if message has vision content\n            const hasVisionContent = attachments === null || attachments === void 0 ? void 0 : attachments.some((att)=>att.type === 'image');\n            // Validate vision capability\n            if (hasVisionContent && !(modelCapabilities === null || modelCapabilities === void 0 ? void 0 : modelCapabilities.supportsVision)) {\n                setError(\"Model \".concat(selectedModel, \" does not support vision/image processing. Please select a vision-capable model like llava or qwen2-vl.\"));\n                setLoading(false);\n                return;\n            }\n            // Prepare messages for API\n            const conversationMessages = [\n                ...messages,\n                userMessage\n            ];\n            // Only include tools if model supports them\n            const toolsToSend = (modelCapabilities === null || modelCapabilities === void 0 ? void 0 : modelCapabilities.supportsTools) ? availableTools : [];\n            console.log('ChatWindow - Available tools:', availableTools);\n            console.log('ChatWindow - Model capabilities:', modelCapabilities);\n            console.log('ChatWindow - Tools to send:', toolsToSend);\n            // Send to Ollama API\n            const response = await fetch('/api/ollama', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    model: selectedModel,\n                    messages: conversationMessages.map((msg)=>({\n                            role: msg.role,\n                            content: msg.content,\n                            ...msg.toolCall && {\n                                tool_calls: [\n                                    msg.toolCall\n                                ]\n                            }\n                        })),\n                    tools: toolsToSend,\n                    hasVisionContent\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to get response from Ollama');\n            }\n            const data = await response.json();\n            const assistantMessage = data.message;\n            console.log('Received assistant message:', assistantMessage);\n            console.log('Model capabilities:', modelCapabilities);\n            // Create assistant message\n            const newAssistantMessage = {\n                id: (0,uuid__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(),\n                role: 'assistant',\n                content: assistantMessage.content,\n                timestamp: new Date()\n            };\n            // Check if there are tool calls (only if model supports tools)\n            if ((modelCapabilities === null || modelCapabilities === void 0 ? void 0 : modelCapabilities.supportsTools) && assistantMessage.tool_calls && assistantMessage.tool_calls.length > 0) {\n                console.log('Tool calls detected:', assistantMessage.tool_calls);\n                const toolCall = assistantMessage.tool_calls[0];\n                // Add an ID to the tool call if it doesn't have one\n                if (!toolCall.id) {\n                    toolCall.id = (0,uuid__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n                }\n                newAssistantMessage.toolCall = toolCall;\n                // Add the assistant message with tool call\n                onMessageSent(newAssistantMessage);\n                // Execute the tool call\n                await executeToolCall(toolCall, [\n                    ...messages,\n                    userMessage,\n                    newAssistantMessage\n                ]);\n            } else {\n                // Add the assistant message\n                onMessageSent(newAssistantMessage);\n            }\n        } catch (err) {\n            console.error('Error sending message:', err);\n            setError('Failed to send message. Please try again.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const executeToolCall = async (toolCall, currentMessages)=>{\n        try {\n            console.log('Executing tool call:', toolCall);\n            // Parse tool arguments\n            let toolArgs;\n            try {\n                toolArgs = typeof toolCall.function.arguments === 'string' ? JSON.parse(toolCall.function.arguments) : toolCall.function.arguments;\n            } catch (parseError) {\n                console.error('Failed to parse tool arguments:', parseError);\n                toolArgs = toolCall.function.arguments;\n            }\n            console.log('Tool arguments:', toolArgs);\n            // Determine server name based on tool name\n            let serverName = 'exa'; // default\n            if (toolCall.function.name === 'get_weather') {\n                serverName = 'weather';\n            } else if (toolCall.function.name === 'search_web') {\n                serverName = 'exa';\n            }\n            console.log(\"Using server: \".concat(serverName, \" for tool: \").concat(toolCall.function.name));\n            // Execute tool via MCP API\n            const response = await fetch('/api/mcp', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'execute_tool',\n                    serverName: serverName,\n                    toolName: toolCall.function.name,\n                    arguments: toolArgs\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to execute tool');\n            }\n            const toolResult = await response.json();\n            // Create tool result message\n            const toolResultMessage = {\n                id: (0,uuid__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(),\n                role: 'tool',\n                content: JSON.stringify(toolResult.result, null, 2),\n                timestamp: new Date(),\n                toolResult: {\n                    toolCallId: toolCall.id,\n                    result: toolResult.result\n                }\n            };\n            onMessageSent(toolResultMessage);\n            // Send the tool result back to Ollama for final response\n            const finalResponse = await fetch('/api/ollama', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    model: selectedModel,\n                    messages: [\n                        ...currentMessages,\n                        toolResultMessage\n                    ].map((msg)=>({\n                            role: msg.role,\n                            content: msg.content,\n                            ...msg.toolCall && {\n                                tool_calls: [\n                                    msg.toolCall\n                                ]\n                            }\n                        })),\n                    tools: availableTools\n                })\n            });\n            if (finalResponse.ok) {\n                const finalData = await finalResponse.json();\n                const finalMessage = {\n                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(),\n                    role: 'assistant',\n                    content: finalData.message.content,\n                    timestamp: new Date()\n                };\n                onMessageSent(finalMessage);\n            }\n        } catch (err) {\n            console.error('Error executing tool call:', err);\n            // Add error message\n            const errorMessage = {\n                id: (0,uuid__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(),\n                role: 'assistant',\n                content: 'Sorry, I encountered an error while using the tool. Please try again.',\n                timestamp: new Date()\n            };\n            onMessageSent(errorMessage);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        display: \"flex\",\n        flexDirection: \"column\",\n        height: \"100%\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                children: error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: -20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        y: -20\n                    },\n                    transition: {\n                        duration: 0.3\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        sx: {\n                            p: 2\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            severity: \"error\",\n                            onClose: ()=>setError(null),\n                            sx: {\n                                borderRadius: 2,\n                                backdropFilter: 'blur(20px)',\n                                background: 'rgba(255, 68, 68, 0.1)',\n                                border: '1px solid rgba(255, 68, 68, 0.3)',\n                                color: '#ff4444',\n                                '& .MuiAlert-icon': {\n                                    color: '#ff4444'\n                                }\n                            },\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                lineNumber: 275,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                flex: 1,\n                overflow: \"auto\",\n                sx: {\n                    px: 3,\n                    py: 2\n                },\n                children: [\n                    messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.5\n                        },\n                        style: {\n                            height: '100%'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            display: \"flex\",\n                            flexDirection: \"column\",\n                            alignItems: \"center\",\n                            justifyContent: \"center\",\n                            height: \"100%\",\n                            textAlign: \"center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    variant: \"h6\",\n                                    sx: {\n                                        color: 'rgba(255, 255, 255, 0.8)',\n                                        mb: 1,\n                                        fontWeight: 500\n                                    },\n                                    children: conversation ? 'Start chatting' : 'Select a conversation'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    variant: \"body2\",\n                                    sx: {\n                                        color: 'rgba(255, 255, 255, 0.6)',\n                                        maxWidth: 400\n                                    },\n                                    children: conversation ? 'Type a message to begin the conversation with your AI assistant' : 'Choose a conversation from the sidebar or create a new one to get started'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                        lineNumber: 308,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                        children: messages.map((message, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: index * 0.1,\n                                    duration: 0.3\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MessageBubble__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    message: message,\n                                    isUser: message.role === 'user'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 17\n                                }, this)\n                            }, message.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                        lineNumber: 347,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                        children: loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            exit: {\n                                opacity: 0,\n                                y: -20\n                            },\n                            transition: {\n                                duration: 0.3\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TypingIndicator__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                                lineNumber: 373,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                            lineNumber: 367,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                        lineNumber: 365,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                        lineNumber: 378,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                lineNumber: 306,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                sx: {\n                    p: 3,\n                    borderTop: '1px solid rgba(255, 255, 255, 0.1)'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InputBar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    onSendMessage: handleSendMessage,\n                    disabled: !selectedModel || !conversation,\n                    loading: loading,\n                    modelCapabilities: modelCapabilities\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                    lineNumber: 383,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                lineNumber: 382,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n        lineNumber: 273,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatWindow, \"yBUK5kIkV3sR5SA/URI0pxGhTuo=\", true);\n_c = ChatWindow;\nvar _c;\n$RefreshReg$(_c, \"ChatWindow\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/ChatWindow/ChatWindow.tsx\n"));

/***/ })

});