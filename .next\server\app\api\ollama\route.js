/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/ollama/route";
exports.ids = ["app/api/ollama/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Follama%2Froute&page=%2Fapi%2Follama%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Follama%2Froute.ts&appDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Follama%2Froute&page=%2Fapi%2Follama%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Follama%2Froute.ts&appDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_20021792_Documents_Projects_Chat_app_api_ollama_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/ollama/route.ts */ \"(rsc)/./app/api/ollama/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/ollama/route\",\n        pathname: \"/api/ollama\",\n        filename: \"route\",\n        bundlePath: \"app/api/ollama/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\api\\\\ollama\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_20021792_Documents_Projects_Chat_app_api_ollama_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Follama%2Froute&page=%2Fapi%2Follama%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Follama%2Froute.ts&appDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./app/api/ollama/route.ts":
/*!*********************************!*\
  !*** ./app/api/ollama/route.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nconst OLLAMA_BASE_URL = \"http://localhost:11434\" || 0;\n// Function to detect model capabilities based on name and family\nfunction detectModelCapabilities(model) {\n    const modelName = model.name.toLowerCase();\n    const family = model.details?.family?.toLowerCase() || '';\n    // Models known to support tools (verified list)\n    const toolSupportedModels = [\n        'llama3.1',\n        'llama3.2',\n        'llama3.3',\n        'qwen3',\n        'qwen2.5',\n        'qwen2',\n        'mistral-nemo',\n        'mistral',\n        'firefunction',\n        'command-r',\n        'granite3-dense',\n        'granite3-moe',\n        'phi3.5',\n        'deepseek-v3'\n    ];\n    // Models known to support vision\n    const visionSupportedModels = [\n        'llava',\n        'llava-llama3',\n        'llava-phi3',\n        'llava-vicuna',\n        'bakllava',\n        'moondream',\n        'minicpm-v',\n        'qwen2-vl',\n        'internvl',\n        'cogvlm',\n        'yi-vl'\n    ];\n    // Models known for code\n    const codeSupportedModels = [\n        'codellama',\n        'codegemma',\n        'starcoder',\n        'deepseek-coder',\n        'phind-codellama',\n        'wizard-coder',\n        'magicoder',\n        'code-llama'\n    ];\n    const supportsTools = toolSupportedModels.some((supported)=>modelName.includes(supported) || family.includes(supported));\n    const supportsVision = visionSupportedModels.some((supported)=>modelName.includes(supported) || family.includes(supported)) || modelName.includes('vision') || modelName.includes('visual');\n    const supportsCode = codeSupportedModels.some((supported)=>modelName.includes(supported) || family.includes(supported)) || modelName.includes('code');\n    return {\n        supportsTools,\n        supportsVision,\n        supportsCode,\n        contextLength: getContextLength(modelName, family)\n    };\n}\nfunction getContextLength(modelName, family) {\n    // Common context lengths based on model families\n    if (modelName.includes('128k') || family.includes('granite')) return 128000;\n    if (modelName.includes('32k')) return 32000;\n    if (modelName.includes('16k')) return 16000;\n    if (modelName.includes('8k')) return 8000;\n    if (family.includes('llama3')) return 8192;\n    if (family.includes('qwen')) return 32768;\n    if (family.includes('mistral')) return 32768;\n    return 4096; // Default\n}\n// GET /api/ollama - Fetch available models\nasync function GET() {\n    try {\n        const response = await fetch(`${OLLAMA_BASE_URL}/api/tags`);\n        if (!response.ok) {\n            throw new Error(`Ollama API error: ${response.status}`);\n        }\n        const data = await response.json();\n        const models = (data.models || []).map((model)=>({\n                ...model,\n                capabilities: detectModelCapabilities(model)\n            }));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            models\n        });\n    } catch (error) {\n        console.error('Error fetching Ollama models:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch models from Ollama'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/ollama - Send chat message\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { model, messages, tools, hasVisionContent } = body;\n        if (!model) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Model is required'\n            }, {\n                status: 400\n            });\n        }\n        if (!messages || !Array.isArray(messages)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Messages array is required'\n            }, {\n                status: 400\n            });\n        }\n        // Format messages for Ollama API\n        const formattedMessages = messages.map((msg)=>({\n                role: msg.role,\n                content: msg.content,\n                ...msg.toolCall && {\n                    tool_calls: [\n                        msg.toolCall\n                    ]\n                }\n            }));\n        // Get model capabilities to determine what features to include\n        const modelsResponse = await fetch(`${OLLAMA_BASE_URL}/api/tags`);\n        let modelCapabilities = {\n            supportsTools: false,\n            supportsVision: false,\n            supportsCode: false\n        };\n        if (modelsResponse.ok) {\n            const modelsData = await modelsResponse.json();\n            const modelInfo = modelsData.models?.find((m)=>m.name === model);\n            if (modelInfo) {\n                modelCapabilities = detectModelCapabilities(modelInfo);\n            }\n        }\n        // Prepare the request payload\n        const payload = {\n            model,\n            messages: formattedMessages,\n            stream: false\n        };\n        // Only include tools if the model supports them and tools are provided\n        if (tools && tools.length > 0 && modelCapabilities.supportsTools) {\n            payload.tools = tools;\n            console.log(`Sending tools to ${model}:`, JSON.stringify(tools, null, 2));\n            console.log(`Model capabilities:`, modelCapabilities);\n        } else {\n            console.log(`Not sending tools - Model: ${model}, Tools available: ${tools?.length || 0}, Supports tools: ${modelCapabilities.supportsTools}`);\n        }\n        // Validate vision content for non-vision models\n        if (hasVisionContent && !modelCapabilities.supportsVision) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `Model ${model} does not support vision/image processing. Please use a vision-capable model like llava or qwen2-vl.`\n            }, {\n                status: 400\n            });\n        }\n        // Make request to Ollama\n        const response = await fetch(`${OLLAMA_BASE_URL}/api/chat`, {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify(payload)\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error('Ollama API error:', errorText);\n            throw new Error(`Ollama API error: ${response.status}`);\n        }\n        const data = await response.json();\n        // Log the response for debugging\n        console.log('Ollama response:', JSON.stringify(data, null, 2));\n        // Check if the model made tool calls\n        if (data.message.tool_calls && data.message.tool_calls.length > 0) {\n            console.log('Tool calls detected:', data.message.tool_calls);\n        }\n        // Return the response\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: data.message,\n            model: data.model,\n            created_at: data.created_at,\n            done: data.done\n        });\n    } catch (error) {\n        console.error('Error in Ollama chat:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to process chat request'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/ollama/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Follama%2Froute&page=%2Fapi%2Follama%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Follama%2Froute.ts&appDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();