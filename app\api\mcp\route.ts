import { NextRequest, NextResponse } from 'next/server';
import { spawn, ChildProcess } from 'child_process';
import { MCPConfig, Tool } from '@/types';
import fs from 'fs';
import path from 'path';

// Store active MCP server processes
const activeServers: Map<string, ChildProcess> = new Map();

// GET /api/mcp - Get MCP configuration and available tools
export async function GET() {
  try {
    const configPath = path.join(process.cwd(), 'public', 'mcp.config.json');
    
    if (!fs.existsSync(configPath)) {
      return NextResponse.json({ mcpServers: {}, tools: [] });
    }

    const configData = fs.readFileSync(configPath, 'utf8');
    const config: MCPConfig = JSON.parse(configData);

    // Get available tools from all configured servers
    const tools: Tool[] = await getAvailableTools(config);

    return NextResponse.json({
      mcpServers: config.mcpServers,
      tools,
    });
  } catch (error) {
    console.error('Error reading MCP config:', error);
    return NextResponse.json(
      { error: 'Failed to read MCP configuration' },
      { status: 500 }
    );
  }
}

// POST /api/mcp - Execute tool call or update configuration
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, serverName, toolName, arguments: toolArgs, config } = body;

    switch (action) {
      case 'execute_tool':
        return await executeToolCall(serverName, toolName, toolArgs);
      
      case 'update_config':
        return await updateMCPConfig(config);
      
      case 'start_server':
        return await startMCPServer(serverName);
      
      case 'stop_server':
        return await stopMCPServer(serverName);
      
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Error in MCP API:', error);
    return NextResponse.json(
      { error: 'Failed to process MCP request' },
      { status: 500 }
    );
  }
}

// Helper function to get available tools from MCP servers
async function getAvailableTools(config: MCPConfig): Promise<Tool[]> {
  const tools: Tool[] = [];

  console.log('Loading tools from MCP config:', config);

  // Check each configured MCP server and add their tools
  for (const [serverName, serverConfig] of Object.entries(config.mcpServers || {})) {
    console.log(`Processing server: ${serverName}`, serverConfig);

    try {
      // For now, we'll define known tools for each server type
      // In a real implementation, you would start the MCP server and query its capabilities

      if (serverName === 'exa' || serverConfig.description?.toLowerCase().includes('exa')) {
        console.log('Adding Exa search tools');
        tools.push({
          type: 'function',
          function: {
            name: 'search_web',
            description: 'Search the web using Exa search engine',
            parameters: {
              type: 'object',
              properties: {
                query: {
                  type: 'string',
                  description: 'The search query to execute',
                },
                num_results: {
                  type: 'number',
                  description: 'Number of results to return (default: 10)',
                  default: 10,
                },
                include_domains: {
                  type: 'array',
                  items: { type: 'string' },
                  description: 'Domains to include in search',
                },
                exclude_domains: {
                  type: 'array',
                  items: { type: 'string' },
                  description: 'Domains to exclude from search',
                },
              },
              required: ['query'],
            },
          },
        });
      }

      // Add other server types as needed
      if (serverName === 'weather' || serverConfig.description?.toLowerCase().includes('weather')) {
        console.log('Adding weather tools');
        tools.push({
          type: 'function',
          function: {
            name: 'get_weather',
            description: 'Get current weather information for a city',
            parameters: {
              type: 'object',
              properties: {
                city: {
                  type: 'string',
                  description: 'The name of the city to get weather for',
                },
                unit: {
                  type: 'string',
                  enum: ['celsius', 'fahrenheit'],
                  description: 'Temperature unit (default: celsius)',
                  default: 'celsius',
                },
              },
              required: ['city'],
            },
          },
        });
      }

    } catch (error) {
      console.error(`Error loading tools for server ${serverName}:`, error);
    }
  }

  // Always add weather tool for demonstration (since it's simulated)
  if (!tools.some(tool => tool.function.name === 'get_weather')) {
    console.log('Adding default weather tool');
    tools.push({
      type: 'function',
      function: {
        name: 'get_weather',
        description: 'Get current weather information for a city',
        parameters: {
          type: 'object',
          properties: {
            city: {
              type: 'string',
              description: 'The name of the city to get weather for',
            },
            unit: {
              type: 'string',
              enum: ['celsius', 'fahrenheit'],
              description: 'Temperature unit (default: celsius)',
              default: 'celsius',
            },
          },
          required: ['city'],
        },
      },
    });
  }

  console.log(`Loaded ${tools.length} tools:`, tools.map(t => t.function.name));
  return tools;
}

// Execute a tool call on an MCP server
async function executeToolCall(serverName: string, toolName: string, toolArgs: any) {
  try {
    console.log(`Executing tool call: ${serverName}.${toolName}`, toolArgs);

    if (serverName === 'weather' && toolName === 'get_weather') {
      // Simulate weather API call
      const weatherData = {
        city: toolArgs.city,
        temperature: Math.round(Math.random() * 30 + 5), // Random temp between 5-35
        unit: toolArgs.unit || 'celsius',
        condition: ['sunny', 'cloudy', 'rainy', 'snowy'][Math.floor(Math.random() * 4)],
        humidity: Math.round(Math.random() * 100),
        windSpeed: Math.round(Math.random() * 20),
        description: `Current weather in ${toolArgs.city}`,
        timestamp: new Date().toISOString(),
      };

      return NextResponse.json({
        success: true,
        result: weatherData,
      });
    }

    if (serverName === 'exa' && toolName === 'search_web') {
      // Try to use actual Exa API if available, otherwise simulate
      try {
        // Check if we have Exa API key in environment
        const exaApiKey = process.env.EXA_API_KEY;

        if (exaApiKey) {
          // Make actual Exa API call
          const exaResponse = await fetch('https://api.exa.ai/search', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'x-api-key': exaApiKey,
            },
            body: JSON.stringify({
              query: toolArgs.query,
              numResults: toolArgs.num_results || 10,
              includeDomains: toolArgs.include_domains,
              excludeDomains: toolArgs.exclude_domains,
              useAutoprompt: true,
              type: 'search',
            }),
          });

          if (exaResponse.ok) {
            const exaData = await exaResponse.json();
            return NextResponse.json({
              success: true,
              result: {
                results: exaData.results.map((result: any) => ({
                  title: result.title,
                  url: result.url,
                  snippet: result.text || result.snippet || 'No snippet available',
                  publishedDate: result.publishedDate,
                  score: result.score,
                })),
                query: toolArgs.query,
                num_results: exaData.results.length,
              },
            });
          }
        }
      } catch (exaError) {
        console.warn('Exa API call failed, falling back to simulation:', exaError);
      }

      // Fallback to simulation with more realistic data
      const simulatedResults = {
        results: [
          {
            title: `Search Results for "${toolArgs.query}"`,
            url: 'https://example.com/search-result-1',
            snippet: `This is a simulated search result for the query: "${toolArgs.query}". In a real implementation, this would contain actual web search results from the Exa search engine.`,
            publishedDate: new Date().toISOString(),
            score: 0.95,
          },
          {
            title: `Related Information about ${toolArgs.query}`,
            url: 'https://example.com/search-result-2',
            snippet: `Additional context and information related to "${toolArgs.query}". This demonstrates how the search tool would return multiple relevant results.`,
            publishedDate: new Date(Date.now() - 86400000).toISOString(), // Yesterday
            score: 0.87,
          },
        ],
        query: toolArgs.query,
        num_results: toolArgs.num_results || 10,
        note: 'This is a simulated response. Configure EXA_API_KEY environment variable for real search results.',
      };

      return NextResponse.json({
        success: true,
        result: simulatedResults,
      });
    }

    return NextResponse.json(
      { error: `Unknown tool: ${toolName} for server: ${serverName}` },
      { status: 400 }
    );
  } catch (error) {
    console.error('Error executing tool call:', error);
    return NextResponse.json(
      { error: 'Failed to execute tool call' },
      { status: 500 }
    );
  }
}

// Update MCP configuration
async function updateMCPConfig(config: MCPConfig) {
  try {
    const configPath = path.join(process.cwd(), 'public', 'mcp.config.json');
    fs.writeFileSync(configPath, JSON.stringify(config, null, 2));

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error updating MCP config:', error);
    return NextResponse.json(
      { error: 'Failed to update configuration' },
      { status: 500 }
    );
  }
}

// Start an MCP server
async function startMCPServer(serverName: string) {
  try {
    // Implementation would start the actual MCP server process
    // For now, we'll just simulate success
    return NextResponse.json({ success: true, message: `Server ${serverName} started` });
  } catch (error) {
    console.error('Error starting MCP server:', error);
    return NextResponse.json(
      { error: 'Failed to start server' },
      { status: 500 }
    );
  }
}

// Stop an MCP server
async function stopMCPServer(serverName: string) {
  try {
    const process = activeServers.get(serverName);
    if (process) {
      process.kill();
      activeServers.delete(serverName);
    }

    return NextResponse.json({ success: true, message: `Server ${serverName} stopped` });
  } catch (error) {
    console.error('Error stopping MCP server:', error);
    return NextResponse.json(
      { error: 'Failed to stop server' },
      { status: 500 }
    );
  }
}
