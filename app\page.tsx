'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  useTheme,
  useMediaQuery,
  Typography,
  Button,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Stack,
} from '@mui/material';
import {
  Delete as DeleteIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';
import { v4 as uuidv4 } from 'uuid';
import { motion } from 'framer-motion';
import ModelSelector from './components/ModelSelector';
import MCPServerManager from './components/MCPServerManager';
import ToolCapabilitiesPanel from './components/ToolCapabilitiesPanel';
import ChatWindow from './components/ChatWindow/ChatWindow';
import { MCPConfig, Tool, ChatMessage } from './types';

interface Conversation {
  id: string;
  title: string;
  lastMessage: string;
  timestamp: Date;
  messageCount: number;
  messages: ChatMessage[];
}

export default function HomePage() {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const [selectedModel, setSelectedModel] = useState<string | null>(null);
  const [sidebarOpen, setSidebarOpen] = useState(!isMobile);
  const [mcpConfig, setMcpConfig] = useState<MCPConfig>({ mcpServers: {} });
  const [availableTools, setAvailableTools] = useState<Tool[]>([]);
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [activeConversationId, setActiveConversationId] = useState<string | null>(null);
  const [modelCapabilities, setModelCapabilities] = useState<any>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  useEffect(() => {
    loadMCPConfig();
    loadConversations();
  }, []);

  useEffect(() => {
    setSidebarOpen(!isMobile);
  }, [isMobile]);

  const loadMCPConfig = async () => {
    try {
      const response = await fetch('/api/mcp');

      if (response.ok) {
        const data = await response.json();
        console.log('Loaded MCP data:', data);
        console.log('Available tools:', data.tools);
        setMcpConfig({ mcpServers: data.mcpServers || {} });
        setAvailableTools(data.tools || []);
        console.log('Set available tools state:', data.tools || []);
      }
    } catch (error) {
      console.error('Error loading MCP config:', error);
    }
  };

  const loadConversations = () => {
    // Load conversations from localStorage
    const saved = localStorage.getItem('ollama-chat-conversations');
    if (saved) {
      try {
        const parsed = JSON.parse(saved);
        setConversations(parsed.map((conv: any) => ({
          ...conv,
          timestamp: new Date(conv.timestamp),
          messages: conv.messages.map((msg: any) => ({
            ...msg,
            timestamp: new Date(msg.timestamp),
          })),
        })));
      } catch (error) {
        console.error('Error loading conversations:', error);
      }
    }
  };

  const saveConversations = (convs: Conversation[]) => {
    localStorage.setItem('ollama-chat-conversations', JSON.stringify(convs));
  };

  const createNewConversation = () => {
    const newConversation: Conversation = {
      id: uuidv4(),
      title: 'New Conversation',
      lastMessage: '',
      timestamp: new Date(),
      messageCount: 0,
      messages: [],
    };

    const updatedConversations = [newConversation, ...conversations];
    setConversations(updatedConversations);
    setActiveConversationId(newConversation.id);
    saveConversations(updatedConversations);

    if (isMobile) {
      setSidebarOpen(false);
    }
  };

  const selectConversation = (id: string) => {
    setActiveConversationId(id);
    if (isMobile) {
      setSidebarOpen(false);
    }
  };

  const deleteConversation = (id: string) => {
    const updatedConversations = conversations.filter(conv => conv.id !== id);
    setConversations(updatedConversations);
    saveConversations(updatedConversations);

    if (activeConversationId === id) {
      setActiveConversationId(updatedConversations.length > 0 ? updatedConversations[0].id : null);
    }
  };

  const handleDeleteCurrentChat = () => {
    if (activeConversationId) {
      deleteConversation(activeConversationId);
      setDeleteDialogOpen(false);
    }
  };



  const updateConversationWithMessage = (message: ChatMessage) => {
    const activeConv = conversations.find(conv => conv.id === activeConversationId);
    if (!activeConv) return;

    const updatedConversation = {
      ...activeConv,
      messages: [...activeConv.messages, message],
      lastMessage: message.content,
      timestamp: new Date(),
      messageCount: activeConv.messageCount + 1,
      title: activeConv.title === 'New Conversation' && message.role === 'user'
        ? message.content.substring(0, 50) + (message.content.length > 50 ? '...' : '')
        : activeConv.title,
    };

    const updatedConversations = conversations.map(conv =>
      conv.id === activeConversationId ? updatedConversation : conv
    );

    setConversations(updatedConversations);
    saveConversations(updatedConversations);
  };

  const getActiveConversation = () => {
    return conversations.find(conv => conv.id === activeConversationId) || null;
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <Box sx={{ height: '100vh', display: 'flex', overflow: 'hidden' }}>
      {/* Unified Sidebar */}
      <Box
        sx={{
          width: sidebarOpen ? 320 : 0,
          transition: 'width 0.3s ease',
          overflow: 'hidden',
          borderRight: sidebarOpen ? '1px solid rgba(255, 255, 255, 0.1)' : 'none',
        }}
      >
        <Box
          sx={{
            width: 320,
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            backdropFilter: 'blur(20px)',
            background: 'rgba(255, 255, 255, 0.03)',
          }}
        >
          {/* Sidebar Header */}
          <Box sx={{ p: 3, borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
              Ollama Chat
            </Typography>

            {/* Model Selector */}
            <ModelSelector
              selectedModel={selectedModel}
              onModelSelect={setSelectedModel}
              onModelCapabilitiesChange={setModelCapabilities}
            />
          </Box>

          {/* Tool Capabilities Panel */}
          <Box sx={{ p: 2 }}>
            <ToolCapabilitiesPanel
              tools={availableTools}
              onRefresh={loadMCPConfig}
            />
          </Box>

          {/* Conversations */}
          <Box sx={{ flex: 1, overflow: 'auto', p: 2 }}>
            <Box sx={{ mb: 2 }}>
              <Button
                fullWidth
                variant="outlined"
                onClick={createNewConversation}
                sx={{ mb: 2 }}
              >
                New Chat
              </Button>
            </Box>

            {conversations.length === 0 ? (
              <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                No conversations yet
              </Typography>
            ) : (
              conversations.map((conversation) => (
                <Box
                  key={conversation.id}
                  onClick={() => selectConversation(conversation.id)}
                  sx={{
                    p: 2,
                    mb: 1,
                    borderRadius: 2,
                    cursor: 'pointer',
                    background: conversation.id === activeConversationId
                      ? 'rgba(255, 255, 255, 0.1)'
                      : 'transparent',
                    border: conversation.id === activeConversationId
                      ? '1px solid rgba(255, 255, 255, 0.2)'
                      : '1px solid transparent',
                    transition: 'all 0.2s ease',
                    '&:hover': {
                      background: 'rgba(255, 255, 255, 0.05)',
                    },
                  }}
                >
                  <Typography variant="body2" fontWeight={500} noWrap>
                    {conversation.title}
                  </Typography>
                  <Typography variant="caption" color="text.secondary" noWrap>
                    {conversation.messageCount} messages
                  </Typography>
                </Box>
              ))
            )}
          </Box>

          {/* Settings */}
          <Box sx={{ p: 2, borderTop: '1px solid rgba(255, 255, 255, 0.1)' }}>
            <Stack spacing={1}>
              <MCPServerManager
                config={mcpConfig}
                onConfigUpdate={setMcpConfig}
                availableTools={availableTools}
              />

            </Stack>
          </Box>
        </Box>
      </Box>

      {/* Main Chat Area */}
      <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
        {/* Top Bar */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            p: 2,
            borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(20px)',
            background: 'rgba(255, 255, 255, 0.03)',
          }}
        >
          <Button
            onClick={() => setSidebarOpen(!sidebarOpen)}
            sx={{ mr: 2, minWidth: 'auto', p: 1 }}
          >
            ☰
          </Button>

          <Typography variant="h6" sx={{ flex: 1 }}>
            {getActiveConversation()?.title || 'Select a conversation'}
          </Typography>

          {/* Delete Chat Button */}
          {activeConversationId && (
            <Tooltip title="Delete current chat">
              <IconButton
                onClick={() => setDeleteDialogOpen(true)}
                sx={{
                  color: 'rgba(255, 255, 255, 0.7)',
                  '&:hover': {
                    color: '#ff4444',
                    backgroundColor: 'rgba(255, 68, 68, 0.1)',
                  },
                  mr: 1,
                }}
              >
                <DeleteIcon />
              </IconButton>
            </Tooltip>
          )}

          {selectedModel && (
            <Box sx={{ display: 'flex', gap: 1 }}>
              {modelCapabilities?.supportsTools && (
                <Box sx={{
                  px: 1,
                  py: 0.5,
                  borderRadius: 1,
                  background: 'rgba(255, 255, 255, 0.1)',
                  fontSize: '0.75rem'
                }}>
                  Tools
                </Box>
              )}
              {modelCapabilities?.supportsVision && (
                <Box sx={{
                  px: 1,
                  py: 0.5,
                  borderRadius: 1,
                  background: 'rgba(255, 255, 255, 0.1)',
                  fontSize: '0.75rem'
                }}>
                  Vision
                </Box>
              )}
            </Box>
          )}
        </Box>

        {/* Chat Window */}
        <Box sx={{ flex: 1, overflow: 'hidden' }}>
          <ChatWindow
            selectedModel={selectedModel}
            availableTools={availableTools}
            conversation={getActiveConversation()}
            onMessageSent={updateConversationWithMessage}
            modelCapabilities={modelCapabilities}
          />
        </Box>
      </Box>

      {/* Delete Chat Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        slotProps={{
          paper: {
            sx: {
              backdropFilter: 'blur(20px)',
              background: 'rgba(255, 255, 255, 0.1)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              color: 'white',
            },
          },
        }}
      >
        <DialogTitle sx={{ display: 'flex', alignItems: 'center', color: 'white' }}>
          <WarningIcon sx={{ mr: 1, color: '#ff4444' }} />
          Delete Chat
        </DialogTitle>
        <DialogContent>
          <Typography color="rgba(255, 255, 255, 0.8)">
            Are you sure you want to delete "{getActiveConversation()?.title}"?
            This action cannot be undone and all messages in this conversation will be permanently lost.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setDeleteDialogOpen(false)}
            sx={{ color: 'rgba(255, 255, 255, 0.7)' }}
          >
            Cancel
          </Button>
          <Button
            onClick={handleDeleteCurrentChat}
            variant="contained"
            sx={{
              bgcolor: '#ff4444',
              '&:hover': { bgcolor: '#ff3333' }
            }}
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
    </motion.div>
  );
}
